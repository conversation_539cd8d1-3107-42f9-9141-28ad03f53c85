

server {

        # 默认端口是80，根据需要更改
        listen       80;

        # 域名解析
        # 也可以修改为域名：示例（server_name  test.com;）其他地方访问：http://test.com
        # 也可以修改为 ip：示例（server_name  127.0.0.1;）其他地方访问：http://服务器ip:端口
        root  /usr/share/nginx/html;
        server_name  localhost; # (本机访问：http://localhost/，其他地方访问：http://服务器ip)
        error_page  404           /404.html;
        error_page   500 502 503 504  /50x.html;
        # 如果是/api/student 开始得地址则转发到后端
        location /api/student {
                proxy_pass http://**************:8001;
        }
        # 根目录
        location / {
                # vue项目的打包后的dist

        index  index.html;
        # autoindex on;
                #  history 模式下需要加上这一行
        try_files $uri $uri/ /index.html;
        }
}
