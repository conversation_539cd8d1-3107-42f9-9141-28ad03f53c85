import type { App } from "vue";
import { createPinia } from "pinia";
// 数据持久化
import { createPersistedState } from "pinia-plugin-persistedstate";

// 导入子模块
import useAppStore from "./modules/app";
import useApplicationStore from "./modules/application";
import useCompanyUserStore from "./modules/companyUser";
import useUserStore from "./modules/user";

// 安装pinia状态管理插件
function setupStore(app: App) {
  console.log("setupStore", app);

  const pinia = createPinia();

  const piniaPersist = createPersistedState({
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  });
  pinia.use(piniaPersist);

  app.use(pinia);
}

// 导出模块
export { useApplicationStore, useAppStore, useCompanyUserStore, useUserStore };
export default setupStore;
