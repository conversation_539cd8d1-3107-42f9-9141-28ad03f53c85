// import type { AppState } from "./types";
import { defineStore } from "pinia";

const useAppStore = defineStore("app", () => {
  const allNavBarHeight = ref<number>(0);
  const systemInfo = ref<UniApp.GetSystemInfoResult>();
  const menuButtonRect = ref<UniApp.GetMenuButtonBoundingClientRectRes>(
    {} as UniApp.GetMenuButtonBoundingClientRectRes,
  );

  const getSystemInfo = computed<UniApp.GetSystemInfoResult | undefined>(() => {
    return systemInfo.value;
  });
  const getAllNavBarHeight = computed<number>(() => {
    return allNavBarHeight.value;
  });

  const initSystemInfo = () => {
    const res: UniApp.GetSystemInfoResult = uni.getSystemInfoSync();
    systemInfo.value = res;
  };
  const initMenuButtonBoundingClientRect = () => {
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-QQ || MP-TOUTIAO
    // 小程序环境下，使用官方API获取胶囊按钮位置
    const res = uni.getMenuButtonBoundingClientRect();
    menuButtonRect.value = res;
    // #endif
  };
  const checkUpdate = () => {
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-QQ || MP-TOUTIAO || APP-PLUS
    // 小程序和App环境下，使用更新管理器
    try {
      const updateManager = uni.getUpdateManager();
      updateManager.onCheckForUpdate((res: UniApp.OnCheckForUpdateResult) => {
        // 请求完新版本信息的回调
        console.log("检查更新结果:", res.hasUpdate);
      });
      updateManager.onUpdateReady(() => {
        uni.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用?",
          success(res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          },
        });
      });
      updateManager.onUpdateFailed((res: any) => {
        console.error("更新失败:", res);
        // 新的版本下载失败
        uni.showToast({
          title: "更新失败",
          icon: "error",
        });
      });
    } catch (error) {
      console.error("获取更新管理器失败:", error);
    }
    // #endif

    // #ifdef H5
    // H5环境下，可以通过其他方式检查更新，例如版本号比对
    console.log("H5环境下，可以通过版本号比对或其他方式检查更新");
    // 这里可以添加H5环境下的更新检查逻辑
    // #endif
  };

  const setAllNavBarHeight = (data: number) => {
    allNavBarHeight.value = data;
  };
  return {
    allNavBarHeight,
    systemInfo,
    menuButtonRect,
    getSystemInfo,
    getAllNavBarHeight,
    initSystemInfo,
    initMenuButtonBoundingClientRect,
    checkUpdate,
    setAllNavBarHeight,
  };
});

export default useAppStore;
