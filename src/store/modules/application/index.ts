import type { StudentInternshipListRes } from "@/api/internshipProgram/types";
import type { List } from "@/components/common/type";
import { defineStore } from "pinia";

const useApplicationStore = defineStore(
  "application",
  () => {
    const currentPlanInfo = ref<StudentInternshipListRes>({});
    const searchValueList = ref<List[][]>([]);

    const setCurrentPlanInfo = (value: StudentInternshipListRes) => {
      currentPlanInfo.value = value;
    };

    const setSearchValueList = (value: List[][]) => {
      searchValueList.value = value;
    };

    const initSearchValueList = () => {
      searchValueList.value = [];
    };

    return {
      searchValueList,
      setSearchValueList,
      initSearchValueList,
      currentPlanInfo,
      setCurrentPlanInfo,
    };
  },
  {
    persist: true,
  },
);

export default useApplicationStore;
