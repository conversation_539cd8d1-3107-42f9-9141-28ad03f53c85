import type { LoginReq } from "@/api/companyTeacher/types";
import type { CompanyUserState } from "./types";
import { CompanyTeacherApi } from "@/api";
import { clearToken, CompanyTokenKey, getToken, setToken } from "@/utils/auth";
import { defineStore } from "pinia";

const useCompanyUserStore = defineStore("companyUser", {
  state: (): CompanyUserState => ({
    token: getToken(CompanyTokenKey) || "",
  }),
  actions: {
    // 异步登录并存储token
    login(loginForm: LoginReq) {
      return new Promise((resolve, reject) => {
        CompanyTeacherApi.login(loginForm)
          .then((res) => {
            console.log("login res", res);
            const token = res.token;
            if (token) {
              setToken(token, CompanyTokenKey);
              this.token = token;
            }
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 登出
    logout() {
      clearToken(CompanyTokenKey);
      this.token = "";
    },
    // 获取token
    getToken() {
      return getToken(CompanyTokenKey);
    },
    // 设置token
    setToken(token: string) {
      setToken(token, CompanyTokenKey);
      this.token = token;
    },
  },
  persist: true,
});

export default useCompanyUserStore;
