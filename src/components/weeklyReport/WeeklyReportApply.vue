<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <WeeklyReportApplyInfo
      ref="weeklyReportApplyInfoRef"
      v-model="weeklyReportApplyInfoData"
      :type="type"
      :plan-id="planId" />
    <WeeklyReportContent
      ref="weeklyReportContentRef"
      v-model="weeklyReportContentData"
      :plan-id="planId" />
    <up-button
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onSbmit"></up-button>
  </view>
</template>

<script lang="ts" setup>
import type { IWeeklyReportApplyInfo, IWeeklyReportContent } from "./type";
import { WeeklyReportApi } from "@/api";
import { useLoading } from "@/hooks";
import { Toast } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  studentWeeklyReportId: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: "",
  },
  isAdd: {
    type: Boolean,
    default: true,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

onMounted(() => {
  props.isEdit && getStudentWeeklyReportDetail();
});

const { showLoading, hideLoading } = useLoading();
// 获取月报详情
const getStudentWeeklyReportDetail = async () => {
  try {
    showLoading();
    const data = await WeeklyReportApi.getStudentWeeklyReportDetail({
      studentWeeklyReportId: props.studentWeeklyReportId,
    });

    data.attachment = data.attachment && JSON.parse(data.attachment);
    const {
      title,
      week_number,
      report_start_date,
      report_end_date,
      content,
      attachment,
    } = data;
    Object.assign(weeklyReportApplyInfoData.value, {
      title,
      week_number,
      report_start_date,
      report_end_date,
    });
    Object.assign(weeklyReportContentData.value, { content, attachment });
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const weeklyReportApplyInfoRef = ref();
const weeklyReportContentRef = ref();

const weeklyReportApplyInfoData = ref<IWeeklyReportApplyInfo>({
  title: "",
  week_number: "",
  report_start_date: "",
  report_end_date: "",
});
const weeklyReportContentData = ref<IWeeklyReportContent>({
  content: "",
  attachment: [],
});

const onSbmit = () => {
  Promise.all([
    weeklyReportApplyInfoRef.value?.formRef.validate(),
    weeklyReportContentRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      try {
        showLoading();
        const allInfo: any = {
          ...weeklyReportApplyInfoData.value,
          ...weeklyReportContentData.value,
          attachment: JSON.stringify(
            weeklyReportContentData.value.attachment || "",
          ),
        };

        if (props.isAdd) {
          await WeeklyReportApi.submitStudentWeeklyReport(allInfo, {
            internshipPlanId: props.planId,
          });
          Toast("提交成功");
        } else {
          await WeeklyReportApi.editStudentWeeklyReport(allInfo, {
            studentWeeklyReportId: props.studentWeeklyReportId,
          });
          Toast("修改成功");
        }

        uni.navigateBack();
      } catch (err) {
        console.log(err);
      } finally {
        hideLoading();
      }
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    })
    .finally(() => {
      hideLoading();
    });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
