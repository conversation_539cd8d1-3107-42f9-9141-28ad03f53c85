<template>
  <view class="rounded-[24rpx] bg-white px-24rpx py-10rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="周报内容"
        prop="content"
        required
        :custom-style="customStyle">
        <up-textarea
          v-model="formInfo.content"
          border="none"
          placeholder="请输入内容"
          height="300rpx"
          :maxlength="-1"></up-textarea>
        <Upload
          v-model="formInfo.attachment"
          :max-count="10"
          multiple
          :action="action" />
        <view v-if="formInfo.attachment?.length" class="mt-24rpx">
          <UploadList
            :file-list="formInfo.attachment"
            @delete="(val) => onDelete(val, 'attachment')" />
        </view>
      </up-form-item>
    </up-form>
  </view>
</template>

<script lang="ts" setup>
import type { IWeeklyReportContent } from "./type";
import { WeeklyReportApi } from "@/api";
import { action } from "@/config";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

watch(
  () => props.planId,
  (val) => {
    val && getDailyReportRequirement();
  },
);

// 获取学生日报实习计划日报要求
const maxWord = ref<number>(500);
const getDailyReportRequirement = async () => {
  const data = await WeeklyReportApi.getWeeklyReportRequirement({
    internshipPlanId: props.planId,
  });
  maxWord.value = data?.max_word_count_requirement || 500;
  console.log("getUnsubmittedDateList", data);
};

const customStyle = ref({ display: "block", marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IWeeklyReportContent>({
  default: () => ({
    content: "",
    attachment: [],
  }),
});
const rules = ref({
  content: {
    type: "string",
    required: true,
    min: maxWord.value,
    message: `内容至少${maxWord.value}字`,
    trigger: ["blur", "change"],
  },
});

const onDelete = (file, key) => {
  formInfo.value[key] = formInfo.value?.[key]?.filter((item) => item !== file);
};

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-steps-item__content {
  margin-top: 0 !important;
}

::v-deep .u-textarea {
  padding: 18rpx 0 32rpx;
}

.content-box {
  padding: 18rpx 24rpx;
  border: 2rpx solid rgb(155 155 155 / 10%);
  border-radius: 24rpx;
}

::v-deep .u-form-item__body__right__content__slot {
  display: block;
}
</style>
