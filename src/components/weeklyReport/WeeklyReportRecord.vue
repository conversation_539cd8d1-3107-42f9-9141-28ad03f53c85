<template>
  <view v-if="list?.length" class="p-24rpx">
    <view v-for="(item, index) in list" :key="index">
      <CardItem
        :item="item"
        :init-data="initData"
        :is-show-image="false"
        :type="ApplyEnum.readOver"
        :title="`${userStore.name}的周报`">
        <view flex justify-end gap-x-16rpx>
          <up-button
            v-if="item.audit_status === ReadOverEnum.pending"
            :custom-style="customStyle"
            color="#FFE7E6"
            text="撤销"
            shape="circle"
            @click="onRevoke(item)"></up-button>
          <up-button
            v-else-if="
              item.audit_status === ReadOverEnum.rejected ||
              item.audit_status === ReadOverEnum.revoked
            "
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="onResubmit(item)"></up-button>
          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="详情"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </view>
      </CardItem>
    </view>
  </view>
  <view v-else mt-300rpx>
    <Empty />
  </view>
</template>

<script lang="ts" setup>
import type { GetStudentWeeklyReportRes } from "@/api/weeklyReport/types";
import { WeeklyReportApi } from "@/api";
import { ApplyEnum, ModeEnum, ReadOverEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

// 获取学生周报记录
const getStudentWeeklyReport = async () => {
  try {
    showLoading();
    const data = await WeeklyReportApi.getStudentWeeklyReport({
      internshipPlanId: props.planId,
    });
    list.value = data.map((item) => {
      return {
        ...item,
        time: `${item.report_start_date} - ${item.report_end_date}`,
      };
    });
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const init = debounce(async () => {
  try {
    await getStudentWeeklyReport();
  } catch (err) {
    console.log(err);
  }
}, 300);

watch(
  () => props.planId,
  () => {
    init();
  },
);

onShow(() => {
  if (props.planId) {
    init();
  }
});

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});
const list = ref<GetStudentWeeklyReportRes[]>([]);
const initData = ref([
  {
    label: "标题",
    key: "title",
  },
  {
    label: "周次",
    key: "week_number",
  },
  {
    label: "日期",
    key: "time",
  },
]);

const { showModal } = useModal();
const onRevoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await WeeklyReportApi.revokeStudentWeeklyReport({
        studentWeeklyReportId: item.id,
      });
      Toast("撤销成功");
      getStudentWeeklyReport();
    }
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
const onResubmit = async (item) => {
  uni.navigateTo({
    url: `/pages/active/weeklyReport/index?mode=${ModeEnum.EDIT}&studentWeeklyReportId=${item.id}`,
  });
};
const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/weeklyReport/detail?studentWeeklyReportId=${item.id}`,
  });
};
</script>

<style lang="scss" scoped></style>
