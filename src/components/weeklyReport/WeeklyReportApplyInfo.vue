<template>
  <view class="rounded-[24rpx] bg-white px-24rpx py-10rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="标题"
        prop="title"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.title"
          disabled-color="#ffffff"
          placeholder="请填写标题30字以内"
          input-align="right"
          maxlength="30"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="周次"
        prop="weekStr"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onDate">
        <up-input
          v-model="formInfo.weekStr"
          disabled-color="#ffffff"
          placeholder="请填写"
          style="pointer-events: none"
          input-align="right"
          disabled
          border="none"></up-input>
        <template v-if="props.type === PageEnum.patch" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="开始日期"
        prop="report_start_date"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.report_start_date"
          disabled
          disabled-color="#ffffff"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="结束日期"
        prop="report_end_date"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.report_end_date"
          disabled
          disabled-color="#ffffff"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <up-picker
      ref="uPickerRef"
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      @change="onChange"
      @cancel="onCancel"
      @confirm="onSure" />
  </view>
</template>

<script lang="ts" setup>
import type { IWeeklyReportApplyInfo } from "./type";
import { WeeklyReportApi } from "@/api";
import { PageEnum } from "@/api/enums/value";
import { useLoading } from "@/hooks";
import usePicker from "@/hooks/usePicker";
import { getCurrentWeekInfo, getWeekDateRange } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: "",
  },
});

const { showLoading, hideLoading } = useLoading();

onMounted(() => {
  const weekInfo = getCurrentWeekInfo();
  console.log("weekInfo===", weekInfo);
  const { year, startDate, endDate, weekNumber } = weekInfo;
  Object.assign(formInfo.value, {
    weekStr: `${year}年第${weekNumber}周`,
    week_number: weekNumber,
    report_start_date: startDate,
    report_end_date: endDate,
  });
});

// 获取学生未提交的周报的周次列表
const columnData = ref();
const getUnsubmittedWeekList = async () => {
  try {
    showLoading();
    const data = await WeeklyReportApi.getUnsubmittedWeekList({
      internshipPlanId: props.planId,
    });
    columnData.value = data.weeks;
    columns.value[0] = data.years;
    columns.value[1] = data.weeks?.[0];
    console.log("getUnsubmittedWeekList", data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IWeeklyReportApplyInfo>({
  default: () => ({
    title: "",
    weekStr: "",
    week_number: "",
    report_start_date: "",
    report_end_date: "",
  }),
});
const rules = ref({
  title: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  week_number: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  report_start_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  report_end_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

const onDate = async () => {
  console.log("props.type", props.type);
  if (props.type === PageEnum.patch) {
    await getUnsubmittedWeekList();
    showModal("week_number");
  }
};

const uPickerRef = ref();
const onChange = (e) => {
  const { columnIndex, index } = e;
  if (columnIndex === 0) {
    uPickerRef.value.setColumnValues(1, columnData[index]);
  }
};
const onSure = (e) => {
  showPicker.value = false;
  formInfo.value.week_number = e.value;

  formInfo.value.weekStr = `${e.value[0]}年第${e.value[1]}周`;
  const { startDate, endDate } = getWeekDateRange(e.value[0], e.value[1]);
  formInfo.value.report_start_date = startDate;
  formInfo.value.report_end_date = endDate;
};

const { showPicker, showModal, onCancel } = usePicker(formInfo);
const columns = ref<any>([]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<style lang="scss" scoped></style>
