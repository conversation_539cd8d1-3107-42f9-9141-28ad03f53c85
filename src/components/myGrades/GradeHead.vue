<template>
  <view center flex-col pb-32rpx pt-64rpx>
    <view class="mb-32rpx text-72rpx text-#D63C38 font-600 leading-[40rpx]">
      {{ totalScore ?? "待提交" }}
    </view>
    <view class="text-32rpx text-#212121 font-400 leading-[40rpx]">
      综合得分
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  totalScore: {
    type: Number,
    default: 0,
  },
});
</script>

<style lang="scss" scoped></style>
