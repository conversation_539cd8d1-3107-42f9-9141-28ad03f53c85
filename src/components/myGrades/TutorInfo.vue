<template>
  <ItemBox title="带教老师">
    <view class="flex flex-col gap-y-32rpx pt-32rpx text-28rpx font-400">
      <view class="between">
        <view class="text-#616161">姓名</view>
        <view class="text-#212121">{{ detail.teacher_name }}</view>
      </view>
      <view class="between">
        <view class="text-#616161">联系方式</view>
        <view class="text-#212121">{{ detail.teacher_phone_number }}</view>
      </view>
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
defineProps({
  detail: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="scss" scoped></style>
