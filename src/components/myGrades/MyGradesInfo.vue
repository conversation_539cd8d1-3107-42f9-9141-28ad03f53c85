<template>
  <view class="bg relative flex items-center px-24rpx py-48rpx">
    <image
      class="mr-20rpx h-88rpx w-88rpx"
      :src="scoreInfo.student_info?.avatar || '/static/images/ic_touxiang.png'"
      mode="scaleToFill" />
    <view>
      <view class="text-34rpx text-#212121 font-500 leading-[48rpx]">
        {{ scoreInfo.student_info?.name }}
      </view>
      <view class="text-24rpx text-#9B9B9B font-400 leading-[34rpx]">
        {{ scoreInfo.student_info?.class_name }}
      </view>
    </view>
    <view
      absolute
      right-24rpx
      top--64rpx
      h-240rpx
      w-240rpx
      rounded-full
      bg-white
      p-10rpx>
      <Arcbar :series-data="scoreInfo.score" />
    </view>
  </view>
  <view
    class="info-box"
    flex
    flex-col
    gap-64rpx
    bg-white
    px-24rpx
    pb-122rpx
    pt-64rpx>
    <view v-for="(item, index) in list" :key="index" @click="item.fun">
      <view mb-16rpx flex items-center justify-between>
        <view
          class="flex items-center text-30rpx text-#212121 font-500 leading-[42rpx]">
          <view mr-24rpx>{{ item.name }}</view>
          <view>{{ item.percentage }}%</view>
        </view>
        <view
          v-if="item.score"
          class="text-32rpx text-#D63C38 font-400 leading-[44rpx]"
          flex
          items-center>
          <view>{{ item.show_word }}</view>
          <view
            v-if="item.can_go_to_detail"
            class="i-ep-arrow-right text-28rpx text-#ccc"></view>
        </view>
        <view
          v-else
          class="text-26rpx text-#212121 font-400 leading-[36rpx]"
          flex
          items-center>
          <view class="circular bg-[#FFA769]"></view>
          <view>{{ item.show_word }}</view>
          <view
            v-if="item.can_go_to_detail"
            class="i-ep-arrow-right text-28rpx text-#ccc"></view>
        </view>

        <!-- <view
          v-else
          class="text-26rpx text-#212121 font-400 leading-[36rpx]"
          flex
          items-center>
          <view class="circular bg-#C4C4C4"></view>
          <view>暂未考评</view>
          <view class="i-ep-arrow-right text-28rpx text-#ccc"></view>
        </view> -->
      </view>
      <up-line-progress
        active-color="#D63C38"
        :percentage="item.score || 0"
        :show-text="false"></up-line-progress>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ModeEnum } from "@/api/enums/value";

const props = defineProps({
  scoreInfo: {
    type: Object,
    default: () => ({}),
  },
});

const list = ref<any>();
watch(
  () => props.scoreInfo,
  (val) => {
    list.value =
      val.internship_assessment
        ?.map((item) => {
          return {
            ...item,
            fun: () => {
              const handle = {
                // 校内教师评分
                internal_teacher_score_assessment: () => {
                  uni.navigateTo({
                    url: `/pages/active/myGrades/guideGrading?assessmentType=${item.item_id}`,
                  });
                },
                // 实习前考核
                before_internship_assessment: () => {
                  uni.navigateTo({
                    url: `/pages/active/questionnaire/index?assessmentType=${item.item_id}`,
                  });
                },
                // 企业综合评分
                company_teacher_score_assessment: () => {
                  const url = item.score
                    ? "/pages/active/myGrades/corporateRatingDetail"
                    : "/pages/active/myGrades/shareRate";
                  uni.navigateTo({
                    url: `${url}?assessmentType=${item.item_id}`,
                  });
                },
                // 实习后考核
                after_internship_assessment: () => {
                  uni.navigateTo({
                    url: `/pages/active/questionnaire/index?assessmentType=${item.item_id}`,
                  });
                },
                // 自我评分
                self_score_assessment: () => {
                  const isDetail = item.score !== null;
                  uni.navigateTo({
                    url: `/pages/active/myGrades/selfAssessment?assessmentType=${item.item_id}&mode=${isDetail ? ModeEnum.DETAIL : ModeEnum.ADD}`,
                  });
                },
              };
              item.can_go_to_detail &&
                handle[item.item_id] &&
                handle[item.item_id]();
            },
          };
        })
        .filter((item) => item.show) || [];
  },
);
</script>

<style lang="scss" scoped>
.bg {
  background: linear-gradient(180deg, #fff9f9 80%, #fff 100%);
  border-radius: 24rpx 24rpx 0 0;
}

.info-box {
  border-radius: 0 0 24rpx 24rpx;
}

.circular {
  @apply w-14rpx h-14rpx mr-12rpx rounded-full;
}
</style>
