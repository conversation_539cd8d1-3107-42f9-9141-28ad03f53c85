<template>
  <view class="rounded-[24rpx] bg-white px-24rpx py-10rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="标题"
        prop="title"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.title"
          disabled-color="#ffffff"
          placeholder="请填写标题30字以内"
          input-align="right"
          maxlength="30"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="日期"
        prop="report_date"
        required
        :custom-style="customStyle"
        @click="onDate">
        <up-input
          v-model="formInfo.report_date"
          disabled
          disabled-color="#ffffff"
          placeholder="请选择"
          style="pointer-events: none"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
    </up-form>

    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="showDate = false"
      @confirm="onDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      @cancel="onCancel"
      @confirm="onConfirm" />
  </view>
</template>

<script lang="ts" setup>
import type { IDailyReportApplyInfo } from "./type";
import { DailyReportApi } from "@/api";
import { PageEnum } from "@/api/enums/value";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: "",
  },
});

// 获取未交日报的日期列表
const getUnsubmittedDateList = async () => {
  const data = await DailyReportApi.getUnsubmittedDateList({
    internshipPlanId: props.planId,
  });
  columns.value[0] = data;
  console.log("getUnsubmittedDateList++++++", columns.value);
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IDailyReportApplyInfo>({
  default: () => ({
    title: "",
    report_date: "",
  }),
});
const rules = ref({
  title: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  report_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

const onDate = async () => {
  console.log("props.type", props.type);
  if (props.type === PageEnum.patch) {
    await getUnsubmittedDateList();
  }
  if (props.type === PageEnum.patch) {
    showModal("report_date");
  } else {
    onShowDate("report_date");
  }
};

// 时间选择
const { dataValue, showDate, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<string[][]>([]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<style lang="scss" scoped></style>
