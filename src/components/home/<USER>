<template>
  <view mt-24rpx rounded-24rpx bg-white px-24rpx py-32rpx>
    <view class="mb-24rpx text-(32rpx #212121) font-500 leading-[40rpx]">
      待办事项
    </view>
    <view flex flex-wrap gap-15rpx>
      <view
        v-for="(item, index) in items"
        :key="index"
        h-284rpx
        w="[calc(50%-8rpx)]"
        p40rpx
        :style="{
          background: `url(${item.bgimage})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
        @click="item.fun">
        <view h-80rpx w-80rpx>
          <image :src="item.icon" mode="aspectFit" block h-full w-full />
        </view>
        <view mb-12rpx mt-32rpx flex items-center>
          <view class="mr-12rpx text-(30rpx #fff) font-500 leading-[40rpx]">
            {{ item.label }}
          </view>
          <image
            block
            h-20rpx
            w-20rpx
            src="/static/images/arrowRight.png"
            mode="scaleToFill" />
        </view>

        <view class="text-(24rpx #212121 white/69) font-400 leading-[32rpx]">
          {{ item.subLabel }}
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { TodoTitleRes } from "@/api/home/<USER>";
import bgImage1 from "@/static/images/home/<USER>";
import bgImage2 from "@/static/images/home/<USER>";
import bgImage3 from "@/static/images/home/<USER>";
import bgImage4 from "@/static/images/home/<USER>";

const props = defineProps({
  todoTitle: {
    type: Object as PropType<TodoTitleRes>,
    default: () => ({}),
  },
});

const items = ref();
watch(
  () => props.todoTitle,
  (val) => {
    console.log("todoTitle====", val);
    items.value = [
      {
        icon: "/static/images/home/<USER>",
        bgimage: bgImage1,
        label: "实习申请",
        subLabel: val.internship_application,
        fun: () => {
          uni.navigateTo({
            url: "/pages/active/internshipApplication/index",
          });
        },
      },
      {
        icon: "/static/images/home/<USER>",
        bgimage: bgImage2,
        label: "实习考核",
        subLabel: val.internship_assessment,
        fun: () => {
          uni.navigateTo({
            url: "/pages/active/myGrades/index",
          });
        },
      },
      {
        icon: "/static/images/home/<USER>",
        bgimage: bgImage3,
        label: "签到提醒",
        subLabel: val.internship_sign_in,
        fun: () => {
          uni.navigateTo({
            url: "/pages/active/signIn/index",
          });
        },
      },
      {
        icon: "/static/images/home/<USER>",
        bgimage: bgImage4,
        label: "周报待提交",
        subLabel: val.internship_weekly_report,
        fun: () => {
          uni.navigateTo({
            url: "/pages/active/weeklyReport/index",
          });
        },
      },
    ];
  },
);
</script>

<style lang="scss" scoped></style>
