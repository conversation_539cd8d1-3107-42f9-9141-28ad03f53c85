<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <MonthlyReportApplyInfo
      ref="monthlyReportApplyInfoRef"
      v-model="monthlyReportApplyInfoData"
      :type="type"
      :plan-id="planId" />
    <MonthlyReportContent
      ref="monthlyReportContentRef"
      v-model="monthlyReportContentData"
      :plan-id="planId" />
    <up-button
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onSbmit"></up-button>
  </view>
</template>

<script lang="ts" setup>
import type { IMonthlyReportApplyInfo, IMonthlyReportContent } from "./type";
import { MonthlyReportApi } from "@/api";
import { useLoading } from "@/hooks";
import { Toast } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  studentMonthlyReportId: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: "",
  },
  isAdd: {
    type: Boolean,
    default: true,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const { showLoading, hideLoading } = useLoading();

onMounted(() => {
  props.isEdit && getStudentMonthlyReportDetail();
});

// 获取月报详情
const getStudentMonthlyReportDetail = async () => {
  try {
    showLoading();
    const data = await MonthlyReportApi.getStudentMonthlyReportDetail({
      studentMonthlyReportId: props.studentMonthlyReportId,
    });
    data.attachment = data.attachment && JSON.parse(data.attachment);
    const { title, report_date, content, attachment } = data;
    Object.assign(monthlyReportApplyInfoData.value, { title, report_date });
    Object.assign(monthlyReportContentData.value, { content, attachment });
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const monthlyReportApplyInfoRef = ref();
const monthlyReportContentRef = ref();

const monthlyReportApplyInfoData = ref<IMonthlyReportApplyInfo>({
  title: "",
  month: "",
  year: "",
  report_start_date: "",
  report_end_date: "",
});
const monthlyReportContentData = ref<IMonthlyReportContent>({
  content: "",
  attachment: [],
});
const onSbmit = () => {
  Promise.all([
    monthlyReportApplyInfoRef.value?.formRef.validate(),
    monthlyReportContentRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      try {
        showLoading();
        const allInfo: any = {
          ...monthlyReportApplyInfoData.value,
          ...monthlyReportContentData.value,
          attachment: JSON.stringify(
            monthlyReportContentData.value.attachment || "",
          ),
        };

        if (props.isAdd) {
          await MonthlyReportApi.submitStudentMonthlyReport(allInfo, {
            internshipPlanId: props.planId,
          });
          Toast("申请成功");
        } else {
          await MonthlyReportApi.editStudentMonthlyReport(allInfo, {
            studentMonthlyReportId: props.studentMonthlyReportId,
          });
          Toast("修改成功");
        }
        uni.navigateBack();
      } catch (err) {
        console.error(err);
      } finally {
        hideLoading();
      }
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
