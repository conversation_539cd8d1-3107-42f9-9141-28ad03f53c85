<template>
  <view class="rounded-[24rpx] bg-white p-24rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="标题"
        prop="title"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.title"
          disabled-color="#ffffff"
          placeholder="请填写标题30字以内"
          input-align="right"
          maxlength="30"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="月份"
        prop="monthStr"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onDate">
        <up-input
          v-model="formInfo.monthStr"
          disabled-color="#ffffff"
          placeholder="请选择"
          style="pointer-events: none"
          input-align="right"
          disabled
          border="none"></up-input>
        <template v-if="props.type === PageEnum.patch" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="开始日期"
        prop="report_start_date"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.report_start_date"
          disabled
          disabled-color="#ffffff"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="结束日期"
        prop="report_end_date"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.report_end_date"
          disabled
          disabled-color="#ffffff"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <up-picker
      ref="uPickerRef"
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      @change="onChange"
      @cancel="onCancel"
      @confirm="onSure" />
  </view>
</template>

<script lang="ts" setup>
import type { IMonthlyReportApplyInfo } from "./type";
import { MonthlyReportApi } from "@/api";
import { PageEnum } from "@/api/enums/value";
import { useLoading } from "@/hooks";
import usePicker from "@/hooks/usePicker";
import { getCurrentMonthDateRange, getMonthRange } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: "",
  },
});

const { showLoading, hideLoading } = useLoading();

onMounted(() => {
  const monthInfo = getCurrentMonthDateRange();
  console.log("monthInfo===", monthInfo);
  const { year, month, startDate, endDate } = monthInfo;
  Object.assign(formInfo.value, {
    monthStr: `${year}年${month}月`,
    month,
    year,
    report_start_date: startDate,
    report_end_date: endDate,
  });
});

// 获取未交月报的月份列表
const columnData = ref();
const getUnsubmittedMonthList = async () => {
  try {
    showLoading();
    const data = await MonthlyReportApi.getUnsubmittedMonthList({
      internshipPlanId: props.planId,
    });
    columnData.value = data.months;
    columns.value[0] = data.years;
    columns.value[1] = data.months?.[0];
    console.log("getUnsubmittedMonthList", data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IMonthlyReportApplyInfo>({
  default: () => ({
    title: "",
    monthStr: "",
    month: "",
    report_start_date: "",
    report_end_date: "",
  }),
});
const rules = ref({
  title: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  month: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  report_start_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  report_end_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

const onDate = async () => {
  console.log("props.type", props.type);
  if (props.type === PageEnum.patch) {
    await getUnsubmittedMonthList();
    showModal("report_date");
  }
};

const uPickerRef = ref();
const onChange = (e) => {
  const { columnIndex, index } = e;
  if (columnIndex === 0) {
    uPickerRef.value.setColumnValues(1, columnData[index]);
  }
};
const onSure = (e) => {
  showPicker.value = false;
  formInfo.value.month = e.value;
  formInfo.value.monthStr = `${e.value[0]}年${e.value[1]}月`;

  const { start, end } = getMonthRange(e.value[0], e.value[1]);
  formInfo.value.report_start_date = start;
  formInfo.value.report_end_date = end;
};

const { showPicker, showModal, onCancel } = usePicker(formInfo);
const columns = ref<any>([]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<style lang="scss" scoped></style>
