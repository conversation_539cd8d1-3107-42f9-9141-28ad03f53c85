<template>
  <view class="rounded-[24rpx] bg-white p-24rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="月报内容"
        prop="content"
        required
        :custom-style="customStyle">
        <up-textarea
          v-model="formInfo.content"
          border="none"
          placeholder="请输入内容"
          height="300rpx"
          :maxlength="-1"></up-textarea>
        <Upload
          v-model="formInfo.attachment"
          :max-count="10"
          multiple
          :action="action" />
        <view v-if="formInfo.attachment?.length" class="mt-24rpx">
          <UploadList
            :file-list="formInfo.attachment"
            @delete="(val) => onDelete(val, 'attachment')" />
        </view>
      </up-form-item>
    </up-form>
  </view>
</template>

<script lang="ts" setup>
import type { IMonthlyReportContent } from "./type";
import { MonthlyReportApi } from "@/api";
import { action } from "@/config";
import { useLoading } from "@/hooks";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const { showLoading, hideLoading } = useLoading();

watch(
  () => props.planId,
  (val) => {
    val && getDailyReportRequirement();
  },
);

// 获取学生日报实习计划日报要求
const maxWord = ref<number>(1000);
const getDailyReportRequirement = async () => {
  try {
    showLoading();
    const data = await MonthlyReportApi.getMonthlyReportRequirement({
      internshipPlanId: props.planId,
    });
    maxWord.value = data.max_word_count_requirement as number;
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ display: "block", marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IMonthlyReportContent>({
  default: () => ({
    content: "",
    attachment: [],
  }),
});
const rules = ref({
  content: {
    type: "string",
    required: true,
    min: maxWord.value,
    message: `内容至少${maxWord.value}字`,
    trigger: ["blur", "change"],
  },
});

const onDelete = (file, key) => {
  formInfo.value[key] = formInfo.value?.[key]?.filter((item) => item !== file);
};

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-steps-item__content {
  margin-top: 0 !important;
}

::v-deep .u-textarea {
  padding: 18rpx 0 32rpx;
}

.content-box {
  padding: 18rpx 24rpx;
  border: 2rpx solid rgb(155 155 155 / 10%);
  border-radius: 24rpx;
}

::v-deep .u-form-item__body__right__content__slot {
  display: block;
}
</style>
