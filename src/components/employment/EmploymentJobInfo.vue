<template>
  <ItemBox title="岗位信息">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="岗位名称"
        prop="position_name"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.position_name"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          :disabled="!isSelf"
          border="none"></up-input>
        <template v-if="!isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <!-- <up-form-item
        label="岗位类别"
        prop="industry"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.industry"
          disabled
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item> -->
      <up-form-item
        label="岗位介绍"
        prop="position_content"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.position_content"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="工作内容"
        prop="internship_content"
        border-bottom
        required
        :custom-style="{
          display: 'block',
          ...customStyle,
        }">
        <up-textarea
          v-model="formInfo.internship_content"
          :disabled="!isSelf"
          :custom-style="{
            marginTop: '16rpx',
            background: '#ffffff',
            padding: 0,
          }"
          placeholder="请填写工作内容"
          border="none"></up-textarea>
      </up-form-item>
      <up-form-item
        label="工作地点"
        prop="position_location"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.position_location"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          :disabled="!isSelf"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure" />
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IEmploymentJobInfo } from "./type";
import { InternshipProgramApi } from "@/api";
import { useLoading } from "@/hooks";
import usePicker from "@/hooks/usePicker";
import { type IOption, listToIOption } from "@/utils/option";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  isSelf: {
    type: Boolean,
    default: false,
  },

  internshipPlanId: {
    type: Number,
    default: 0,
  },
  companyId: {
    type: Number,
    default: 0,
  },
});

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>(null);
const formInfo = defineModel<IEmploymentJobInfo>({
  default: () => ({
    position_name: "",
    position_id: undefined,
    position_content: "",
    internship_content: "",
    position_location: "",
  }),
});
const rules = ref({
  value1: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  value2: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  value3: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value4: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value5: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value6: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value7: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

const { showLoading, hideLoading } = useLoading();

watch(
  () => props.companyId,
  (val) => {
    val && getPositionList();
  },
);

// 获取实习计划岗位列表
const getPositionList = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getPositionList({
      internshipPlanId: props.internshipPlanId,
      companyId: props.companyId,
    });
    console.log("getPositionList", data);
    columns.value[0] = listToIOption(data, "position_id", "position_name");
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

// 获取岗位详情
const getPositionDetail = async (id) => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getPositionDetail({
      positionId: id,
    });
    console.log("getPositionDetail", data);
    formInfo.value.position_id = data.id;
    Object.assign(formInfo.value, data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const onSure = (e) => {
  onConfirm(e);
  getPositionDetail(e.value[0]?.value);
};

const { showPicker, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<IOption[][]>([]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}

.matching-degree {
  background-image: url("@/static/images/matchingDegree.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
