<template>
  <ItemBox title="就业信息">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="合同开始日期"
        prop="contract_start_date"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onShowDate('contract_start_date')">
        <up-input
          v-model="formInfo.contract_start_date"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>

      <up-form-item
        label="合同结束日期"
        prop="contract_end_date"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowDate('contract_end_date')">
        <up-input
          v-model="formInfo.contract_end_date"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="合同性质"
        prop="contractNatureLabel"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowModel('contractNatureLabel')">
        <up-input
          v-model="formInfo.contractNatureLabel"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>

      <up-form-item
        label="专业是否对口"
        prop="matchLabel"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowModel('matchLabel')">
        <up-input
          v-model="formInfo.matchLabel"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="是否签订协议"
        prop="signedAgreementLabel"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowModel('signedAgreementLabel')">
        <up-input
          v-model="formInfo.signedAgreementLabel"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="薪酬"
        prop="position_salary"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.position_salary"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="企业人数"
        prop="company_people_number"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_people_number"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="企业联系人"
        prop="company_contact"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_contact"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="showDate = false"
      @confirm="onDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure" />
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import type { IEmploymentInfo } from "./type";
import { enumApi } from "@/api/enums";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>(null);
const formInfo = defineModel<IEmploymentInfo>({
  default: () => ({
    contract_start_date: "",
    contract_end_date: "",
    contract_nature: "",
    contractNatureLabel: "",
    is_match: "",
    matchLabel: "",
    is_signed_agreement: "",
    signedAgreementLabel: "",
    position_salary: "",
    company_people_number: undefined,
    company_contact: "",
  }),
});
const rules = ref({
  value1: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  value2: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  value3: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value4: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value5: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value6: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  value7: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

// 时间选择
const { dataValue, showDate, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

const columns = ref<IOption[][]>([]);
const onShowModel = (key) => {
  if (key === "contractNatureLabel") {
    columns.value[0] = enumApi.get("CONTRACT").options;
  } else {
    columns.value[0] = enumApi.get("YN").options;
  }
  showModal(key);
};
const onSure = (e) => {
  onConfirm(e);
  if (currentKey.value === "contract_nature") {
    formInfo.value.contract_nature = e.value[0]?.value;
  } else if (currentKey.value === "signedAgreementLabel") {
    formInfo.value.is_signed_agreement = e.value[0]?.value;
  } else if (currentKey.value === "matchLabel") {
    formInfo.value.is_match = e.value[0]?.value;
  }
};
const { currentKey, showPicker, showModal, onCancel, onConfirm } =
  usePicker(formInfo);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}

.matching-degree {
  background-image: url("@/static/images/matchingDegree.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
