<template>
  <ItemBox title="企业信息">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="实习单位"
        prop="company_name"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_name"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          :disabled="!isSelf"
          border="none"></up-input>
        <template v-if="!isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="企业性质"
        prop="company_nature"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onShowModel('company_nature')">
        <up-input
          v-model="formInfo.company_nature"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template v-if="isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="行业类别"
        prop="company_industry"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowModel('company_industry')">
        <up-input
          v-model="formInfo.company_industry"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template v-if="isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        ref="item4"
        label="统一社会信用代码"
        prop="credit_code"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.credit_code"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="有效日期"
        prop="business_license_validity"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowDate('business_license_validity')">
        <up-input
          v-model="formInfo.business_license_validity"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template v-if="isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="法人/负责人"
        prop="company_principal"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_principal"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>

      <up-form-item
        label="联系电话"
        prop="company_phone"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_phone"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="企业地址"
        prop="company_address"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_address"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="onDateCancel"
      @confirm="onDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure"></up-picker>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import type { IEmploymentInformation } from "./type";
import { InternshipProgramApi, JianliApi } from "@/api";
import { useLoading } from "@/hooks";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";
import { listToIOption } from "@/utils/option";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  isSelf: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Number,
    default: 1,
  },
});

const emit = defineEmits(["change"]);

const { showLoading, hideLoading } = useLoading();

onMounted(async () => {
  try {
    showLoading();
    await Promise.all([
      getBaseData(1), // 1企业性质
      getBaseData(2), // 2行业类别
      getCompanyList(),
    ]);
  } finally {
    hideLoading();
  }
});

const natureOptions = ref<IOption[]>([]);
const industrOptions = ref<IOption[]>([]);
const getBaseData = async (dataType: number) => {
  const data = await JianliApi.getBaseData({ dataType });
  if (dataType === 1) {
    natureOptions.value = listToIOption(data, "id", "name");
  } else if (dataType === 2) {
    industrOptions.value = listToIOption(data, "id", "name");
  }
};

// 获取实习计划公司列表
const getCompanyList = async () => {
  const data = await InternshipProgramApi.getCompanyList({
    internshipPlanId: props.id,
  });
  console.log("getCompanyList", data);
  columns.value[0] = listToIOption(data, "company_id", "company_name");
};

// 获取公司详情
const getCompanyDetail = async (id: number) => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getCompanyDetail({
      companyId: id,
    });
    formInfo.value.company_id = data.id;
    Object.assign(formInfo.value, data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IEmploymentInformation>({
  default: () => ({
    company_name: "",
    company_id: undefined,
    company_nature: "",
    company_industry: "",
    credit_code: "",
    business_license_validity: "",
    company_principal: "",
    company_people_number: "",
    company_phone: "",
    company_address: "",
  }),
});
const rules = ref({
  company_name: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  company_nature: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  company_industry: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  credit_code: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  company_people_number: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  company_principal: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  company_phone: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  company_address: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
});

// 时间选择
const { dataValue, showDate, onDateCancel, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

const beforePicker = (key: string) => {
  const handle = {
    company_name: () => {},
    company_nature: () => {},
    company_industry: () => {},
    company_people_number: () => {},
  };
  handle[key] && handle[key]();
};

const columns = ref<IOption[][]>([]);
const onShowModel = (key: string) => {
  if (!props.isSelf) return;
  if (key === "company_nature") {
    columns.value[0] = natureOptions.value;
  } else if (key === "company_industry") {
    columns.value[0] = industrOptions.value;
  }

  showModal(key);
};

const onSure = (e: any) => {
  onConfirm(e);
  if (!props.isSelf) {
    getCompanyDetail(e.value[0]?.value);
    emit("change", e.value[0]);
  } else {
    formInfo.value[currentKey.value] = e.value[0]?.value;
  }
};
const { currentKey, showPicker, showModal, onCancel, onConfirm } = usePicker(
  formInfo,
  beforePicker,
);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}
</style>
