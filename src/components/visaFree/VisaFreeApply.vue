<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <VisaFreeApplyInfo
      ref="visaFreeApplyInfoRef"
      v-model="visaFreeApplyInfoData" />
    <VisaFreeApplyContent
      ref="visaFreeApplyContentRef"
      v-model="visaFreeApplyContentData" />
    <UploadImg v-model="uploadImgData" />
    <up-button
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onSbmit"></up-button>
  </view>
</template>

<script lang="ts" setup>
import type { SubmitFreeAttendanceReq } from "@/api/visaFree/types";
import type {
  IUploadImg,
  IVisaFreeApplyContent,
  IVisaFreeApplyInfo,
} from "./type";
import { VisaFreeApi } from "@/api";
import { useLoading } from "@/hooks";
import { Toast } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  freeAttendanceId: {
    type: Number,
    default: 0,
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const { showLoading, hideLoading } = useLoading();

onMounted(() => {
  props.isEdit && getFreeAttendanceDetail();
});

// 获取请假记录详情
const getFreeAttendanceDetail = async () => {
  try {
    showLoading();
    const data = await VisaFreeApi.getFreeAttendanceDetail({
      freeAttendanceId: props.freeAttendanceId,
    });

    const { free_start, free_end, free_days, free_reason } = data;
    Object.assign(visaFreeApplyInfoData.value, {
      free_start,
      free_end,
      free_days,
    });
    Object.assign(visaFreeApplyContentData.value, {
      free_reason,
    });
    uploadImgData.value.attachment =
      data.attachment && JSON.parse(data.attachment);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const visaFreeApplyInfoRef = ref();
const visaFreeApplyContentRef = ref();

const visaFreeApplyInfoData = ref<IVisaFreeApplyInfo>({
  free_start: "",
  free_end: "",
  free_days: undefined,
});
const visaFreeApplyContentData = ref<IVisaFreeApplyContent>({
  free_reason: "",
});
const uploadImgData = ref<IUploadImg>({
  attachment: [],
});

const onSbmit = () => {
  Promise.all([
    visaFreeApplyInfoRef.value?.formRef.validate(),
    visaFreeApplyContentRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      try {
        showLoading();
        const allInfo: SubmitFreeAttendanceReq = {
          ...visaFreeApplyInfoData.value,
          ...visaFreeApplyContentData.value,
          attachment: JSON.stringify(uploadImgData.value.attachment),
        };

        if (props.isAdd) {
          await VisaFreeApi.submitFreeAttendance(allInfo, {
            internshipPlanId: props.planId,
          });
          Toast("申请成功");
        } else {
          await VisaFreeApi.editFreeAttendance(allInfo, {
            freeAttendanceId: props.freeAttendanceId,
          });
          Toast("修改成功");
        }
        uni.redirectTo({
          url: "/pages/active/visaFree/index?type=VisaFreeRecord",
        });
      } catch (err) {
        console.error(err);
      } finally {
        hideLoading();
      }
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
