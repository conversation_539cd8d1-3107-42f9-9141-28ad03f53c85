<template>
  <view class="p-24rpx">
    <view v-for="(item, index) in list" :key="index">
      <CardItem
        :item="item"
        :init-data="initData"
        :is-show-image="false"
        :title="`${userStore.name}的免签申请`">
        <view v-if="item.audit_status === StatusEnum.pending">
          <up-button
            :custom-style="{
              border: '1px solid #9B9B9B',
              color: '#9B9B9B',
              width: '155rpx',
              height: '56rpx',
            }"
            text="撤销申请"
            shape="circle"
            @click="onRevoke(item)"></up-button>
        </view>
        <view
          v-else-if="
            item.audit_status === StatusEnum.rejected ||
            item.audit_status === StatusEnum.revoked
          ">
          <up-button
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="onResubmit(item)"></up-button>
        </view>
        <view>
          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="详情"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </view>
      </CardItem>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { VisaFreeApi } from "@/api";
import { enumApi } from "@/api/enums";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

// 获取免签记录
const getFreeAttendanceRecord = async () => {
  try {
    showLoading();
    const data = await VisaFreeApi.getFreeAttendanceRecord({
      internshipPlanId: props.planId,
    });

    list.value = data.map((item) => {
      const startDay = enumApi.get("LEAVEDAY").findLabel(item.free_start_day);
      const endDay = enumApi.get("LEAVEDAY").findLabel(item.free_end_day);
      return {
        ...item,
        leaveDate: `${item.free_start_date} ${startDay} - ${item.free_end_date} ${endDay}`,
      };
    });
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const init = debounce(async () => {
  if (props.planId) {
    getFreeAttendanceRecord();
  }
}, 500);

onMounted(() => {
  init();
});

watch(
  () => props.planId,
  () => {
    init();
  },
);

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});
const list = ref<any>([]);
const initData = ref([
  {
    label: "实习单位",
    key: "company_name",
  },
  {
    label: "申请日期",
    key: "leaveDate",
  },
  {
    label: "申请事由",
    key: "free_reason",
  },
]);

const { showModal } = useModal();
const onRevoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await VisaFreeApi.revokeFreeAttendance({
        freeAttendanceId: item.id,
      });
      Toast("撤销成功");
      getFreeAttendanceRecord();
    }
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
const onResubmit = async (item) => {
  uni.navigateTo({
    url: `/pages/active/visaFree/index?mode=${ModeEnum.EDIT}&freeAttendanceId=${item.id}`,
  });
};
const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/visaFree/detail?freeAttendanceId=${item.id}`,
  });
};

defineExpose({
  init,
});
</script>

<style lang="scss" scoped></style>
