<template>
  <view class="p-24rpx">
    <view v-for="(item, index) in list" :key="index">
      <CardItem :item="item" :init-data="initData" :is-show-image="false">
        <view flex justify-end gap-x-16rpx>
          <up-button
            v-if="item.audit_status === StatusEnum.pending"
            :custom-style="{
              border: '1px solid #9B9B9B',
              color: '#9B9B9B',
              width: '155rpx',
              height: '56rpx',
            }"
            text="撤销申请"
            shape="circle"
            @click="onRevoke(item)"></up-button>
          <up-button
            v-else-if="
              item.audit_status === StatusEnum.rejected ||
              item.audit_status === StatusEnum.revoked
            "
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="onResubmit(item)"></up-button>
          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="详情"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </view>
      </CardItem>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { GetMakeupAttendanceRecordRes } from "@/api/signIn/types";
import { SignInApi } from "@/api";
import { StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});
const list = ref<GetMakeupAttendanceRecordRes[]>();
const initData = ref([
  {
    label: "异常状态",
    key: "abnormal_type",
  },
  {
    label: "补卡时间",
    key: "attendance_date",
  },
  {
    label: "补卡事由",
    key: "makeup_reason",
  },
]);

const { showLoading, hideLoading } = useLoading();

// 获取补签记录
const getMakeupAttendanceRecord = debounce(async () => {
  try {
    showLoading();
    const data = await SignInApi.getMakeupAttendanceRecord({
      internshipPlanId: props.planId,
    });
    list.value = data;
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
}, 500);

onMounted(() => {
  props.planId && getMakeupAttendanceRecord();
});

watch(
  () => props.planId,
  (val) => {
    val && getMakeupAttendanceRecord();
  },
);

// 撤销补签申请
const revokeMakeupAttendance = async (id) => {
  await SignInApi.revokeMakeupAttendance({
    makeupAttendanceId: id,
  });
};

const { showModal } = useModal();
const onRevoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await revokeMakeupAttendance(item.id);
      showLoading();
      Toast("撤销成功");
      getMakeupAttendanceRecord();
    }
  } catch (err) {
    console.log(err);
  }
};
const onResubmit = async (item) => {
  await showModal("您确定要重新提交申请吗？");
  Toast("提交成功");
};
const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/signIn/detail?makeupAttendanceId=${item.id}`,
  });
};
</script>

<style lang="scss" scoped></style>
