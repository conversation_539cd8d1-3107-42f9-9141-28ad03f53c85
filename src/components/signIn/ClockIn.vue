<template>
  <view v-if="isFreeSign" p-24rpx>
    <view h-800rpx center rounded-24rpx bg-white p-24rpx>
      <up-empty
        width="600rpx"
        height="100%"
        icon="/static/images/tip/img_mian.png"
        text="今日免签（休息日）"></up-empty>
    </view>
  </view>
  <view v-else class="p-24rpx">
    <view class="rounded-[24rpx] bg-white px-24rpx py-32rpx">
      <Steps
        :list="stepsList"
        :company-position="dataInfo?.company_position_location"
        :step-current="stepCurrent" />
      <view mb-48rpx mt-64rpx>
        <Check :button-info="buttonInfo" @check-in="onCheckIn" />
      </view>
    </view>
    <view class="btn_box">
      <up-button
        text="免签申请"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="goApplication"></up-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type {
  But<PERSON>,
  Check,
  GetTodayAttendanceInfoRes,
} from "@/api/signIn/types";
import { SignInApi } from "@/api";
import { CheckEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const { showLoading, hideLoading } = useLoading();

// 获取学生今日签到信息
const dataInfo = ref<GetTodayAttendanceInfoRes>();
const isFreeSign = ref<boolean>(false);
const buttonInfo = ref<Button>();
// 当前步骤
const stepCurrent = ref(0);
// 获取当前时间格式化字符串
const getCurrentTimeString = () => {
  const now = new Date();
  return {
    date: now.toLocaleDateString("zh-CN"),
    time: now.toLocaleTimeString("zh-CN", { hour12: false }),
    full: now.toLocaleString("zh-CN"),
  };
};
// 签到步骤列表
const stepsList = ref<Check[]>([]);
const getTodayAttendanceInfo = async () => {
  try {
    showLoading();
    const data = await SignInApi.getTodayAttendanceInfo({
      internshipPlanId: props.planId,
    });
    isFreeSign.value = false;
    dataInfo.value = data;
    buttonInfo.value = data.button;
    stepsList.value = data.check;
    stepCurrent.value = data.attendance_type === "start" ? 0 : 1;
  } catch (err) {
    isFreeSign.value = true;
    console.log(err);
  } finally {
    hideLoading();
  }
};

const init = debounce(async () => {
  if (props.planId) {
    getTodayAttendanceInfo();
  }
}, 500);

watch(
  () => props.planId,
  () => {
    init();
  },
);

onMounted(() => {
  init();
});

const { showModal } = useModal();
// 处理签到
const onCheckIn = async (location: any) => {
  try {
    console.log("onCheckIn", location);
    showLoading();
    const curSteps = stepsList.value[stepCurrent.value];
    // 先检查位置是否在允许范围内
    const checkPosition = await checkAttendancePosition(location);
    if (!checkPosition && curSteps.frontStatus !== CheckEnum.address_abnormal) {
      stepsList.value[stepCurrent.value].frontStatus =
        CheckEnum.address_abnormal;
      return;
    }

    // 检查是否迟到或早退\
    const attendanceStatus = checkAttendanceTime();
    console.log("考勤状态:", attendanceStatus);
    let msg;
    if (attendanceStatus.status === "late") {
      msg = "您确定要迟到打卡吗？";
    } else if (attendanceStatus.status === "early") {
      msg = "您确定要早退打卡吗？";
    }
    let res: any = { confirm: true };
    if (msg) {
      res = await showModal(msg);
    }
    if (res.confirm) {
      await checkAttendance(location);
      init();
      // 判断是否完成所有签到
      if (stepCurrent.value < stepsList.value.length - 1) {
        stepCurrent.value++;
      }

      // 根据考勤状态显示不同的提示
      if (attendanceStatus.status === "normal") {
        Toast("签到成功");
      } else if (attendanceStatus.status === "late") {
        Toast(`签到成功，您已迟到${attendanceStatus.diffMinutes}分钟`);
      } else if (attendanceStatus.status === "early") {
        Toast(`签到成功，您已早退${attendanceStatus.diffMinutes}分钟`);
      }
    }

    // 位置检查通过，继续签到流程
  } catch (err: any) {
    console.log("签到失败:", err);
  } finally {
    hideLoading();
  }
};

// 学生签到
const checkAttendance = async (location: any) => {
  try {
    const address_abnormal_info =
      stepsList.value[stepCurrent.value]?.address_abnormal_info;
    const abnormal_attachment = JSON.stringify(
      address_abnormal_info?.abnormal_attachment,
    );
    address_abnormal_info.abnormal_attachment = abnormal_attachment;
    const res = await SignInApi.checkAttendance(
      {
        address: location.address,
        lng: location.longitude,
        lat: location.latitude,
        ...address_abnormal_info,
      },
      {
        internshipPlanId: props.planId,
      },
    );
    console.log("checkAttendance", res);
    return Promise.resolve(res); // 返回成功结果
  } catch (err: any) {
    console.log("checkAttendance error:", err);
    // 将错误向上抛出
    throw new Error(`签到失败：${err.message || JSON.stringify(err)}`);
  }
};

// 计算打卡范围
const checkAttendancePosition = async (location: any) => {
  try {
    const res = await SignInApi.checkAttendancePosition(
      {
        lng: location.longitude,
        lat: location.latitude,
      },
      {
        internshipPlanId: props.planId,
      },
    );
    console.log("checkAttendancePosition", res);
    return res; // 返回成功结果
  } catch (err: any) {
    console.log("checkAttendancePosition error:", err);
    // 将错误向上抛出，这样 onCheckIn 方法的 catch 块就能捕获到错误
    return false;
  }
};

/**
 * 检查当前打卡时间是否属于早退或迟到
 * @returns {object} 考勤状态对象，包含状态和时间差（分钟）
 */
const checkAttendanceTime = (): {
  status: "normal" | "late" | "early";
  diffMinutes: number;
} => {
  // 获取当前时间
  const now = new Date().getTime();

  // 从响应式变量中获取上下班时间
  const workTimeStr = dataInfo.value?.work_time;
  const offTimeStr = dataInfo.value?.off_time;

  // 如果没有上下班时间数据，默认为正常
  if (!workTimeStr || !offTimeStr) {
    return { status: "normal", diffMinutes: 0 };
  }

  // 解析上班时间和下班时间
  const workTimeDate = new Date(workTimeStr).getTime();
  const offTimeDate = new Date(offTimeStr).getTime();

  // 判断当前是上班打卡还是下班打卡
  if (stepCurrent.value === 0) {
    // 上班打卡：判断是否迟到
    if (now > workTimeDate) {
      // 计算迟到的分钟数
      const diffMs = now - workTimeDate;
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      // 如果迟到超过1分钟，则标记为迟到
      if (diffMinutes > 1) {
        return { status: "late", diffMinutes };
      }
    }
  } else if (stepCurrent.value === 1) {
    // 下班打卡：判断是否早退
    if (now < offTimeDate) {
      // 计算早退的分钟数
      const diffMs = offTimeDate - now;
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      // 如果早退超过1分钟，则标记为早退
      if (diffMinutes > 1) {
        return { status: "early", diffMinutes };
      }
    }
  }

  // 默认为正常打卡
  return { status: "normal", diffMinutes: 0 };
};

// 前往免签申请
const goApplication = () => {
  uni.navigateTo({
    url: "/pages/active/visaFree/index",
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
.btn_box {
  ::v-deep .u-button {
    height: 88rpx !important;
    margin-top: 32rpx;

    .u-button__text {
      font-size: 32rpx !important;
    }
  }
}
</style>
