<template>
  <view
    v-if="buttonInfo.can_check"
    class="bg m-auto h-274rpx w-274rpx flex flex-col items-center justify-center rounded-[50%] text-center text-white font-500"
    @click="checkIn">
    <view class="text-40rpx leading-[56rpx]">{{ buttonInfo.words }}</view>
    <view class="mt-6rpx text-30rpx leading-[42rpx]">
      {{ currentTime.timeStr }}
    </view>
  </view>
  <view
    v-else
    class="bg-disable m-auto h-274rpx w-274rpx flex flex-col items-center justify-center rounded-[50%] text-center text-white font-500">
    <view class="text-40rpx leading-[56rpx]">今日</view>
    <view class="mt-6rpx text-30rpx leading-[42rpx]">完成签到</view>
  </view>
</template>

<script lang="ts" setup>
import { useLoading } from "@/hooks";
import { checkRunEnvironment, getBrowserLocation } from "@/utils";
import { getLocationByCoords } from "@/utils/amap";
import { getWxLocation, initWxJsSdk } from "@/utils/wx-sdk";

defineProps({
  // mode: {
  //   type: String,
  //   default: "work",
  // },
  buttonInfo: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(["checkIn"]);

const { showLoading, hideLoading } = useLoading();

// 格式化时间
const formatTime = (date: Date) => ({
  dateStr: date.toLocaleDateString("zh-CN"),
  timeStr: date.toLocaleTimeString("zh-CN", { hour12: false }),
  fullStr: date.toLocaleString("zh-CN"),
});

const currentTime = ref(formatTime(new Date()));
const timer = setInterval(() => {
  currentTime.value = formatTime(new Date());
}, 1000);

// 位置信息
const locationInfo = ref({
  latitude: 0,
  longitude: 0,
  address: "",
  formattedAddress: "", // 高德地图返回的格式化地址
  province: "", // 省份
  city: "", // 城市
  district: "", // 区县
  street: "", // 街道
  streetNumber: "", // 门牌号
});

// 是否正在获取位置
const isGettingLocation = ref(false);
// 位置获取重试次数
const MAX_RETRY_COUNT = 1;
const retryCount = ref(0);

const env = checkRunEnvironment();

/**
 * 处理位置获取错误
 */
const handleLocationError = (error: Error) => {
  console.error("获取位置失败:", error);
  // 重置重试次数
  retryCount.value = 0;
  // 通知用户位置获取失败
  uni.showToast({
    title: "获取位置失败，请检查定位权限",
    icon: "none",
  });
  // if (retryCount.value < MAX_RETRY_COUNT) {
  //   retryCount.value++;
  //   console.log(`第 ${retryCount.value} 次重试获取位置`);
  //   getLocation();
  // } else {
  //   // 重置重试次数
  //   retryCount.value = 0;
  //   // 通知用户位置获取失败
  //   uni.showToast({
  //     title: "获取位置失败，请检查定位权限",
  //     icon: "none",
  //   });
  // }
};

/**
 * 获取位置信息
 */
const getLocation = async () => {
  try {
    if (env.isWechat) {
      // 微信浏览器环境
      console.log("微信浏览器环境获取位置");
      try {
        // 初始化微信 JSSDK
        await initWxJsSdk();
        // 获取位置
        const location = await getWxLocation();
        return {
          latitude: location.latitude,
          longitude: location.longitude,
        };
      } catch (error) {
        console.error("微信 JSSDK 获取位置失败:", error);
        throw error;
      }
    } else if (env.isMiniProgram) {
      if (navigator.geolocation) {
        // 实时监听位置变化
        const watchId = navigator.geolocation.watchPosition(
          (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            console.log("实时位置：", lat, lng);
            // 处理位置信息
          },
          (error) => {
            console.error("定位失败：", error);
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000,
          },
        );
      }
    } else {
      // 其他浏览器环境
      const location = await getBrowserLocation(true);
      console.log("浏览器环境获取位置:", location);
      if (location) {
        return location;
      } else {
        throw new Error("浏览器获取位置失败");
      }
    }
  } catch (error) {
    handleLocationError(error as Error);
    throw error;
  }
};

/**
 * 获取详细位置信息
 */
const getDetailedLocation = async (location: any) => {
  if (isGettingLocation.value) {
    return;
  }

  isGettingLocation.value = true;

  try {
    if (!location || !location.latitude || !location.longitude) {
      throw new Error("获取经纬度失败");
    }

    // 更新基本位置信息
    locationInfo.value.latitude = location.latitude;
    locationInfo.value.longitude = location.longitude;
    locationInfo.value.address = `位置: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;

    try {
      // 使用高德地图 API 获取详细位置信息
      const amapLocation = await getLocationByCoords(
        location.longitude,
        location.latitude,
      );

      console.log("获取详细位置信息成功:", amapLocation);

      // 更新详细位置信息
      locationInfo.value = {
        ...locationInfo.value,
        formattedAddress: amapLocation.formattedAddress,
        province: amapLocation.province,
        city: amapLocation.city,
        district: amapLocation.district,
        street: amapLocation.street,
        streetNumber: amapLocation.streetNumber,
        address: amapLocation.formattedAddress,
      };

      return {
        ...location,
        address: amapLocation.formattedAddress,
        formattedAddress: amapLocation.formattedAddress,
        province: amapLocation.province,
        city: amapLocation.city,
        district: amapLocation.district,
        street: amapLocation.street,
        streetNumber: amapLocation.streetNumber,
        time: currentTime.value.fullStr,
      };
    } catch (error) {
      console.error("获取详细位置信息失败:", error);
      // 如果获取详细位置信息失败，仍然返回基本位置信息
      return {
        ...location,
        address: locationInfo.value.address,
        time: currentTime.value.fullStr,
      };
    }
  } catch (error) {
    console.error("获取位置失败:", error);
    throw error;
  } finally {
    isGettingLocation.value = false;
  }
};

onMounted(async () => {
  console.log("====onReady====");
  try {
    const location = await getLocation();
    await getDetailedLocation(location);
    console.log("位置信息:", locationInfo.value);
  } catch (error) {
    console.error("初始获取位置失败:", error);
  }
});

const checkIn = async () => {
  try {
    showLoading();

    // 签到时重新获取位置
    let location;
    try {
      location = await getLocation();
      if (!location) {
        throw new Error("获取位置失败");
      }
    } catch (error) {
      console.error("签到获取位置失败:", error);
      // 获取位置失败时仍允许签到，但使用默认位置信息
      emit("checkIn", {
        latitude: 0,
        longitude: 0,
        address: "位置获取失败",
        time: currentTime.value.fullStr,
      });
      return;
    }

    // 获取详细位置信息
    const detailedLocation = await getDetailedLocation(location);
    console.log("签到位置信息:", detailedLocation);

    // 将位置信息传递给父组件
    emit("checkIn", detailedLocation);
  } catch (error) {
    console.error("签到失败:", error);
    uni.showToast({
      title: "签到失败，请重试",
      icon: "none",
    });
  } finally {
    hideLoading();
  }
};

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(timer);
});
</script>

<style lang="scss" scoped>
.bg {
  background: linear-gradient(272deg, #d63c38 3%, #e76b67 100%);
  box-shadow: 0rpx 8rpx 40rpx 0rpx rgb(225 92 89 / 35%);
}

.bg-disable {
  background: #bfbfbf;
  box-shadow: 0rpx 8rpx 40rpx 0rpx rgb(191 191 191 / 35%);
}
</style>
