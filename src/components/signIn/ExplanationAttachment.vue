<template>
  <ItemBox title="说明附件" sub-title="限传格式为doc，docx，PDF、PNG、JPG">
    <template #titleRight>
      <Upload
        v-model="formInfo.attachment"
        multiple
        :action="action"
        :max-count="10"
        :disabled="disabled" />
    </template>
    <view v-if="formInfo.attachment?.length" class="mt-24rpx">
      <UploadList
        :file-list="formInfo.attachment"
        @delete="(val) => onDelete(val, 'attachment')" />
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IExplanationAttachment } from "./type";
import { action } from "@/config";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref<any>();
const formInfo = defineModel<IExplanationAttachment>({
  default: () => ({
    attachment: [],
  }),
});

const onDelete = (file, key) => {
  formInfo.value[key] = formInfo.value?.[key]?.filter((item) => item !== file);
};

defineExpose({
  formRef,
  formInfo,
});
</script>

<style lang="scss" scoped></style>
