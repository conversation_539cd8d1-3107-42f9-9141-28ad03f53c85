<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <!-- 打卡日历 -->
    <ZsyCalendar
      :sunday-index="0"
      :cell-height="80"
      :month-data="monthData"
      date-active-color="linear-gradient(272deg, #d63c38 3%, #e76b67 100%)"
      @change="onChange"
      @swiper-change="onSwiperChange" />
    <!-- 步骤条 -->
    <view class="rounded-[24rpx] bg-white px-24rpx py-32rpx">
      <template v-if="stepsList?.length">
        <view class="mb-30rpx flex">
          <view
            class="mr-8rpx text-32rpx text-#212121 font-500 leading-[44rpx]">
            {{ selectedDate.split("-")[2] }}
          </view>
          <view
            class="self-end text-24rpx text-#9B9B9B font-400 leading-[38rpx]">
            {{ selectedDate.split("-")[1] }}月
          </view>
        </view>
        <RecordSteps
          v-if="showSteps"
          :step-current="stepCurrent"
          :list="stepsList"
          :company-position="dataInfo?.company_position_location" />
      </template>

      <view v-else h-500rpx center rounded-24rpx bg-white p-24rpx>
        <up-empty
          width="600rpx"
          height="100%"
          icon="/static/images/tip/img_mian.png"
          text="今日免签（休息日）"></up-empty>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { SignInApi } from "@/api";
import { useLoading } from "@/hooks";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const { showLoading, hideLoading } = useLoading();

// 步骤条
const stepCurrent = ref<number>(2);
const stepsList = ref();
// 获取月签到记录
const monthData = ref();
const getMonthAttendanceRecord = async () => {
  try {
    showLoading();
    const data = await SignInApi.getMonthAttendanceRecord({
      internshipPlanId: props.planId,
      year: Number(selectedDate.value.split("-")[0]),
      month: Number(selectedDate.value.split("-")[1]),
    });

    monthData.value = data.month_data;
    // stepsList.value = data.today?.data?.check;
  } catch (err) {
    console.log(err);
  }
  hideLoading();
};

const init = debounce(async () => {
  try {
    getMonthAttendanceRecord();
  } catch (err) {
    console.log(err);
  }
}, 300);

watch(
  () => props.planId,
  () => {
    init();
  },
);

onShow(() => {
  if (props.planId) {
    init();
  }
});

const dataInfo = ref();
const getAttendanceDetail = async () => {
  try {
    showLoading();
    const data = await SignInApi.getAttendanceDetail({
      internshipPlanId: props.planId,
      attendanceDate: selectedDate.value,
    });
    dataInfo.value = data;
    stepsList.value = data.check;
  } catch (err: any) {
    console.log(err);
    if (err.code === 406) {
      stepsList.value = null;
    }
  }
  hideLoading();
};

const showSteps = ref<boolean>(true);
const selectedDate = ref();
const onChange = (e) => {
  console.log("onChange:", e);
  selectedDate.value = e.selectedDate;
  showSteps.value = false;
  nextTick(() => {
    showSteps.value = true;
  });
  getAttendanceDetail();
};
const onSwiperChange = (e) => {
  console.log("onSwiperChange:", e);
  init();
};
</script>

<style lang="scss" scoped></style>
