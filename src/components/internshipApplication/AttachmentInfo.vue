<template>
  <ItemBox title="附件信息" sub-title="限传格式为doc、docx、PDF、PNG、JPG">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="三方协议"
        prop="agreementAttachmentList"
        required
        :custom-style="{
          ...customStyle,
        }">
        <view class="flex justify-end">
          <Upload
            v-model="formData.agreement_attachment"
            multiple
            :action="action"
            :max-count="10"
            :disabled="disabled" />
        </view>
      </up-form-item>
      <view
        v-if="formData.agreement_attachment?.length"
        class="mb-50rpx mt-10rpx">
        <UploadList
          :file-list="formData.agreement_attachment"
          @delete="(val) => onDelete(val, 'agreement_attachment')" />
      </view>
      <up-form-item
        label="实习保险凭证"
        prop="insuranceAttachment"
        required
        :custom-style="customStyle">
        <view class="flex justify-end">
          <Upload
            v-model="formData.insurance_attachment"
            multiple
            :action="action"
            :disabled="disabled" />
        </view>
      </up-form-item>
      <view
        v-if="formData.insurance_attachment?.length"
        class="mb-50rpx mt-10rpx">
        <UploadList
          :file-list="formData.insurance_attachment"
          @delete="(val) => onDelete(val, 'insurance_attachment')" />
      </view>
      <up-form-item
        label="其他"
        prop="otherAttachment"
        required
        :custom-style="customStyle">
        <view class="flex justify-end">
          <Upload
            v-model="formData.other_attachment"
            multiple
            :action="action"
            :max-count="10"
            :disabled="disabled" />
        </view>
      </up-form-item>
      <UploadList
        :file-list="formData.other_attachment"
        @delete="(val) => onDelete(val, 'other_attachment')" />
    </up-form>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { PropType } from "vue";
import type { IAttachmentInfoForm } from "./type";
import { action } from "@/config";
import { computed } from "vue";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  formInfo: {
    type: Object as PropType<IAttachmentInfoForm>,
    required: true,
  },
});

const emit = defineEmits<{
  "update:formInfo": [value: IAttachmentInfoForm];
}>();

console.log("props.action", action);

const formRef = ref<any>();
const customStyle = ref({
  marginLeft: "24rpx",
  display: "flex",
  justifyContent: "space-between",
});

// 使用计算属性来处理双向绑定
const formData = computed({
  get: () => props.formInfo,
  set: (value) => {
    emit("update:formInfo", value);
  },
});

const onDelete = (file, key) => {
  formData.value[key] = formData.value?.[key]?.filter((item) => item !== file);
};

const rules = ref({
  agreement_attachment: {
    type: "array",
    required: true,
    message: "请上传",
    trigger: ["change"],
  },
  insurance_attachment: {
    type: "array",
    required: true,
    message: "请上传",
    trigger: ["change"],
  },
  other_Attachment: {
    type: "array",
    required: true,
    message: "请上传",
    trigger: ["change"],
  },
});

defineExpose({
  formRef,
  formData,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-form-item__body__right__content__slot {
  display: flex;
  justify-content: flex-end;
}
</style>
