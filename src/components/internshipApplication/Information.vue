<template>
  <ItemBox title="企业信息">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="实习单位"
        prop="company_name"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onShowModel">
        <up-input
          v-model="formInfo.company_name"
          disabled
          style="pointer-events: none"
          disabled-color="#ffffff"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template v-if="isSelf" #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="企业性质"
        prop="nature"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.nature"
          disabled
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="行业类别"
        prop="industry_category"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.industry_category"
          disabled
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        ref="item4"
        label="统一社会信用代码"
        prop="credit_code"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.credit_code"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="有效日期"
        prop="business_license_validity"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.business_license_validity"
          disabled
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="法人/负责人"
        prop="principal"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.principal"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <!-- <up-form-item
        label="企业人数"
        prop="business_license_validity"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.business_license_validity"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item> -->
      <up-form-item
        label="联系电话"
        prop="phone_number"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.phone_number"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="企业地址"
        prop="address"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.address"
          :disabled="!isSelf"
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure"></up-picker>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import type { IGetCompanyDetailRes } from "./type";
import { InternshipProgramApi } from "@/api";
import { useLoading } from "@/hooks";
import usePicker from "@/hooks/usePicker";
import { listToIOption } from "@/utils/option";
import { debounce } from "lodash-es";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  isSelf: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Number,
    default: 1,
  },
});

const emit = defineEmits(["change"]);

const { showLoading, hideLoading } = useLoading();

const init = debounce(async () => {
  try {
    await getCompanyList();
  } catch (err) {
    console.log(err);
  }
}, 300);

watch(
  () => props.id,
  () => {
    init();
  },
);

onShow(() => {
  if (props.id) {
    init();
  }
});

// 获取实习计划公司列表
const getCompanyList = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getCompanyList({
      internshipPlanId: props.id,
    });
    console.log("getCompanyList", data);
    columns.value[0] = listToIOption(data, "company_id", "company_name");
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

// 获取公司详情
const getCompanyDetail = async (id) => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getCompanyDetail({
      companyId: id,
    });
    formInfo.value.company_id = data.id;
    Object.assign(formInfo.value, data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<IGetCompanyDetailRes>({
  default: () => ({
    company_name: "",
    company_id: "",
    nature: "",
    industry_category: "",
    credit_code: "",
    business_license_validity: "",
    principal: "",
    phone_number: "",
    address: "",
  }),
});
const rules = ref({
  company_name: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  nature: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  industry_category: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  credit_code: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  business_license_validity: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  principal: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  phone_number: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  address: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
});

const beforePicker = (key: string) => {
  const handle = {
    company_name: () => {},
    nature: () => {},
    industry_category: () => {},
    business_license_validity: () => {},
  };
  handle[key] && handle[key]();
};

const onShowModel = () => {
  if (!props.isSelf) return;
  showModal("company_name");
};

const onSure = (e) => {
  onConfirm(e);
  getCompanyDetail(e.value[0]?.value);
  emit("change", e.value[0]);
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(
  formInfo,
  beforePicker,
);
const columns = ref<IOption[][]>([]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}
</style>
