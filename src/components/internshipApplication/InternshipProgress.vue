<template>
  <view class="mb-24rpx rounded-24rpx bg-white px-24rpx py-32rpx">
    <view class="text-30rpx text-#28292B font-500 leading-[42rpx]">
      {{ item.title }}
    </view>
    <view class="mt-40rpx flex items-center justify-between">
      <RingChart
        v-if="item.sum !== '/' && item.sum"
        :series-data="item.seriesData" />
      <view v-else class="text-#9B9B9B">暂无数据</view>
      <view>
        <view
          class="bg mb-40rpx h-62rpx w-382rpx text-center text-28rpx text-#212121 leading-[62rpx]">
          <text>总数量</text>
          <text ml-48rpx>{{ item.sum }}</text>
        </view>
        <view
          v-for="(subItem, subIndex) in item.seriesData"
          :key="subIndex"
          class="ml-94rpx flex items-center text-#212121 font-400">
          <view
            class="h-16rpx w-16rpx"
            :style="{ backgroundColor: subItem.color }"></view>
          <view class="ml-12rpx mr-48rpx text-24rpx leading-[32rpx]">
            {{ subItem.name }}
          </view>
          <view class="text-28rpx leading-32rpx">{{ subItem.value }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { IProgressData } from "./type";

defineProps({
  item: {
    type: Object as PropType<IProgressData>,
    default: () => ({}),
  },
});
</script>

<style lang="scss" scoped>
.bg {
  background: linear-gradient(
    91deg,
    rgb(255 230 230 / 0%) 0%,
    #ffe6e6 65%,
    rgb(255 230 230 / 0%) 100%
  );
}
</style>
