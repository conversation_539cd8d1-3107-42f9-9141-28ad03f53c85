<template>
  <ItemBox title="实习信息">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="开始时间"
        prop="expected_start_date"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onShowDate('expected_start_date')">
        <up-input
          v-model="formInfo.expected_start_date"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="结束时间"
        prop="expected_end_date"
        border-bottom
        required
        :custom-style="customStyle"
        @click="onShowDate('expected_end_date')">
        <up-input
          v-model="formInfo.expected_end_date"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="专业是否对口"
        prop="matchLabel"
        border-bottom
        required
        :custom-style="customStyle"
        @click="showModal('matchLabel')">
        <up-input
          v-model="formInfo.matchLabel"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="实习薪酬"
        prop="position_salary"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.position_salary"
          :disabled="disabled"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="onDateCancel"
      @confirm="onDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure" />
  </ItemBox>
</template>

<script lang="ts" setup>
import type { StartStudentApplicationRep } from "@/api/internshipProgram/types";
import { enumApi } from "@/api/enums";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>(null);
const formInfo = defineModel<StartStudentApplicationRep>({
  default: () => ({
    expected_start_date: "",
    expected_end_date: "",
    matchLabel: "",
    is_match: "",
    position_salary: "",
  }),
});
const rules = ref({
  expected_start_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  expected_end_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  matchLabel: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["change"],
  },
  position_salary: {
    type: "number",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
});

// 时间选择
const { dataValue, showDate, onDateCancel, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

const onSure = (e) => {
  onConfirm(e);
  formInfo.value.is_match = e.value[0]?.value;
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref([enumApi.get("YN").options]);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}
</style>
