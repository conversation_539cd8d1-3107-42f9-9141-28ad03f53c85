<template>
  <view class="matching-degree h-106rpx flex items-center px-18rpx">
    <view class="mr-24rpx text-28rpx text-#212121 font-400 leading-[40rpx]">
      匹配度
    </view>
    <up-line-progress
      :percentage="matchDegree || 0"
      active-color="#D63C38"
      :show-text="false"></up-line-progress>
    <view class="tex-28rpx ml-24rpx text-#9B9B9B font-400 leading-[40rpx]">
      {{ matchDegree || 0 }}%
    </view>
    <view
      class="ml-24rpx text-30rpx text-#D63C38 font-400 leading-[42rpx]"
      @click="getMatchDegree">
      {{ matchDegree ?? "重新计算" }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import { JianliApi } from "@/api";
import { useLoading } from "@/hooks";

const props = defineProps({
  positionId: {
    type: Number,
    default: 0,
  },
});

const { showLoading, hideLoading } = useLoading();

const matchDegree = ref<string>();

const getMatchDegree = async () => {
  try {
    showLoading();
    const data = await JianliApi.getMatchDegree({
      positionId: props.positionId,
    });
    console.log("getMatchDegree:", data);
    matchDegree.value = data;
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

watch(
  () => props.positionId,
  (val) => {
    console.log("positionId:", val);

    val && getMatchDegree();
  },
  {
    immediate: true,
  },
);
</script>

<style lang="scss" scoped>
.matching-degree {
  background-image: url("@/static/images/matchingDegree.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
