<template>
  <view class="rounded-24rpx bg-white px-24rpx py-32rpx">
    <view class="mb-32rpx text-28rpx text-#212121 font-500 leading-40rpx">
      附件信息
    </view>
    <view mb-56rpx>
      <view class="mb-28rpx text-28rpx text-#616161 leading-40rpx">
        三方协议
      </view>
      <UploadList
        :file-list="formData.agreement_attachment"
        :mode="ModeEnum.DETAIL" />
    </view>
    <view mb-56rpx>
      <view class="mb-28rpx text-28rpx text-#616161 leading-40rpx">
        实习保险凭证
      </view>
      <UploadList
        :file-list="formData.insurance_attachment"
        :mode="ModeEnum.DETAIL" />
    </view>
    <view>
      <view class="mb-28rpx text-28rpx text-#616161 leading-40rpx">其他</view>
      <UploadList
        :file-list="formData.other_attachment"
        :mode="ModeEnum.DETAIL" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { IAttachmentInfoForm } from "./type";
import { ModeEnum } from "@/api/enums/value";

defineProps({
  formData: {
    type: Object as PropType<IAttachmentInfoForm>,
    default: () => ({}),
  },
});
</script>

<style lang="scss" scoped></style>
