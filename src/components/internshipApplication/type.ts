import type {
  GetCompanyDetailRes,
  GetPositionDetailRes,
} from "@/api/internshipProgram/types";
import type { UploadFile } from "../common/type";

export interface IAttachmentInfoForm {
  other_attachment?: UploadFile[];
  insurance_attachment?: UploadFile[];
  agreement_attachment?: UploadFile[];
}

export interface IGetCompanyDetailRes extends GetCompanyDetailRes {
  company_name?: string;
  company_id?: number;
}
export interface IInternshipInfo {
  value1: string;
  value2: string;
  value3: string;
  value4: string;
}
export interface IGetPositionDetailRes extends GetPositionDetailRes {
  position_name?: string;
  position_id?: number;
  internship_content?: string;
  work_time?: string;
  off_time?: string;
  company_teacher_name?: string;
  company_teacher_phone_number?: string;
}
export interface IProgressData {
  title: string;
  sum: string;
  seriesData: ISeriesDatum[];
}

export interface ISeriesDatum {
  name: string;
  value: string;
  color: string;
}
