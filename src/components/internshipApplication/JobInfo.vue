<template>
  <ItemBox title="岗位信息">
    <view mt-28rpx>
      <Match :position-id="formInfo.position_id" />
    </view>
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="岗位名称"
        prop="position_name"
        required
        border-bottom
        :custom-style="customStyle"
        @click="showModal('position_name')">
        <up-input
          v-model="formInfo.position_name"
          :disabled="disabled"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="岗位类别"
        prop="industry"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.industry"
          disabled
          disabled-color="#ffffff"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="岗位介绍"
        prop="description"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.description"
          :disabled="disabled"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="工作内容"
        prop="internship_content"
        border-bottom
        required
        :custom-style="{
          display: 'block',
          ...customStyle,
        }">
        <up-textarea
          v-model="formInfo.internship_content"
          :disabled="disabled"
          border="none"
          :custom-style="{
            marginTop: '16rpx',
          }"
          placeholder="请填写工作内容"
          count></up-textarea>
      </up-form-item>
      <up-form-item
        label="工作地点"
        prop="location"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.location"
          placeholder=""
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="打卡时间"
        prop="internship_content"
        border-bottom
        required
        :custom-style="{
          display: 'block',
          ...customStyle,
        }">
        <view w-full>
          <view pt-20rpx>
            <up-line color="#f1f1f1" dashed></up-line>
          </view>
          <view between items-center py-20rpx @click="onShowDate('work_time')">
            <view flex items-center>
              <view
                class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
              <view>上班时间</view>
            </view>
            <up-input
              v-model="formInfo.work_time"
              style="pointer-events: none"
              placeholder="请选择"
              input-align="right"
              disabled
              disabled-color="#ffffff"
              border="none"></up-input>
            <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
          </view>
          <up-line color="#f1f1f1" dashed></up-line>
          <view flex items-center pt-20rpx @click="onShowDate('off_time')">
            <view flex items-center>
              <view
                class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
              <view>下班时间</view>
            </view>
            <up-input
              v-model="formInfo.off_time"
              placeholder="请选择"
              input-align="right"
              disabled
              style="pointer-events: none"
              disabled-color="#ffffff"
              border="none"
              @click.stop="onShowDate('off_time')"></up-input>
            <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
          </view>
        </view>
      </up-form-item>
      <up-form-item
        label="企业老师"
        prop="company_teacher_name"
        border-bottom
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_teacher_name"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="企业老师电话"
        prop="company_teacher_phone_number"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.company_teacher_phone_number"
          :disabled="disabled"
          placeholder="请填写"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="time"
      @cancel="onDateCancel"
      @confirm="onDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure" />
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import type { IGetPositionDetailRes } from "./type";
import { InternshipProgramApi } from "@/api";
import { useLoading } from "@/hooks";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";
import { listToIOption } from "@/utils/option";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Number,
    default: 1,
  },
  companyId: {
    type: Number,
    default: 0,
  },
});

const { showLoading, hideLoading } = useLoading();

// 获取实习计划岗位列表
const getPositionList = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getPositionList({
      internshipPlanId: props.id,
      companyId: props.companyId,
    });
    console.log("getPositionList", data);
    columns.value[0] = listToIOption(data, "position_id", "position_name");
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

watch(
  () => props.companyId,
  () => {
    getPositionList();
  },
);

// 获取岗位详情
const getPositionDetail = async (id: number) => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getPositionDetail({
      positionId: id,
    });
    console.log("getPositionDetail", data);
    formInfo.value.position_id = data.id;
    Object.assign(formInfo.value, data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const customStyle = ref({ marginLeft: "24rpx" });

const formRef = ref<any>(null);
const formInfo = defineModel<IGetPositionDetailRes>({
  default: () => ({
    position_name: "",
    position_id: "",
    industry: "",
    description: "",
    internship_content: "",
    location: "",
    work_time: "",
    off_time: "",
    company_teacher_name: "",
    company_teacher_phone_number: "",
  }),
});

const rules = ref({
  position_name: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["change"],
  },
  industry: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  description: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  internship_content: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  location: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  company_teacher_name: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  company_teacher_phone_number: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
});

const onSure = (e: any) => {
  onConfirm(e);
  getPositionDetail(e.value[0]?.value);
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<IOption[][]>([]);

// 时间选择
const { dataValue, showDate, onDateCancel, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-textarea {
  padding: 0;
}

::v-deep .u-line {
  border-color: $u-border-color !important;
}

input[disabled] {
  pointer-events: none;
}
</style>
