<template>
  <div class="rounded-24rpx bg-white px-24rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="上一实习结束时间"
        prop="before_internship_actual_end_date"
        required
        border-bottom
        :custom-style="customStyle"
        @click="onShowDate('before_internship_actual_end_date')">
        <up-input
          v-model="formInfo.before_internship_actual_end_date"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
    </up-form>
    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="onDateCancel"
      @confirm="onDateConfirm" />
  </div>
</template>

<script lang="ts" setup>
import useDatePicker from "@/hooks/useDatePicker";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<any>({
  default: () => ({
    before_internship_actual_end_date: "",
  }),
});
const rules = ref({
  before_internship_actual_end_date: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

// 时间选择
const { dataValue, showDate, onDateCancel, onShowDate, onDateConfirm } =
  useDatePicker(formInfo);

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-line {
  border-color: $u-border-color !important;
}
</style>
