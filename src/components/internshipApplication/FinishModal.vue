<template>
  <up-popup
    round="24rpx"
    :show="modelValue"
    mode="center"
    @close="close"
    @open="open">
    <view
      class="bg w-670rpx overflow-hidden rounded-[24rpx] px-60rpx pb-48rpx pt-84rpx">
      <image
        class="m-auto block h-92rpx w-92rpx"
        src="/static/images/ic_tishi.png"
        mode="scaleToFill" />
      <view
        class="my-64rpx text-center text-34rpx text-#212121 font-500 leading-[54rpx]">
        <view
          class="text-center text-40rpx text-#212121 font-500 leading-[56rpx]">
          实习结束申请
        </view>
        <view class="mt-24rpx text-30rpx text-#212121 font-400 leading-[54rpx]">
          您还有任务未完成，是否继续申请结束
        </view>
      </view>
      <view flex gap-24rpx>
        <up-button
          :custom-style="{
            height: '88rpx',
            fontSize: '32rpx',
            color: '#616161',
          }"
          text="取消"
          color="#F6F6F6"
          shape="circle"
          @click="close" />
        <up-button
          :custom-style="{
            height: '88rpx',

            fontSize: '32rpx',
          }"
          text="确定"
          shape="circle"
          color="linear-gradient(272deg, #D63C38 3%, #E76B67 100%)"
          @click="onSave" />
      </view>
    </view>
  </up-popup>
</template>

<script lang="ts" setup>
defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["update:modelValue", "confirm"]);

// 定义方法
const open = () => {
  emit("update:modelValue", true);
};

const close = () => {
  emit("update:modelValue", false);
};

const onSave = () => {
  close();
  emit("confirm");
};
</script>

<style lang="scss" scoped>
.bg {
  background: linear-gradient(178deg, #fce8e8 0%, #fff 100%);
}
</style>
