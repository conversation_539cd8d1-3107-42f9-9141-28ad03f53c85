<template>
  <up-steps :current="stepCurrent" direction="column" active-color="#D63C38">
    <up-steps-item v-for="(item, index) in list" :key="index">
      <template #icon>
        <image
          block
          h-42rpx
          w-42rpx
          :src="icon(item.status)"
          mode="scaleToFill" />
      </template>
      <template #title>
        <view class="mt--5rpx flex items-center">
          <view class="mr-20rpx text-24rpx text-#9B9B9B">{{ item.words }}</view>
          <view class="flex gap-x-15rpx">
            <up-tag
              v-for="(tag, tagIndex) in item.tags"
              :key="tagIndex"
              :text="tag.tag_name"
              plain
              shape="circle"
              text-size="24rpx"
              padding="5rpx 10rpx"
              :border-color="tag.tag_color"
              :color="tag.tag_color"></up-tag>
          </view>
        </view>
      </template>
      <template #desc>
        <view class="py-24rpx text-28rpx text-#212121 font-400 leading-[40rpx]">
          <view v-if="item.check_info" class="mb-24rpx font-500">
            {{ item.check_info?.check_in_time }}
          </view>
          <view class="flex items-center justify-start text-#616161">
            <image
              class="mr-8rpx block h-28rpx w-28rpx"
              src="/static/images/ic_dingwei.png"
              mode="scaleToFill" />
            <view>
              {{ item.check_info?.check_in_address || companyPosition }}
            </view>
          </view>
          <view
            v-if="
              item.status === CheckEnum.address_abnormal ||
              item.frontStatus === CheckEnum.address_abnormal
            "
            ml-36rpx
            mt-12rpx>
            <view
              v-if="item.frontStatus === CheckEnum.address_abnormal"
              class="mb-16rpx text-24rpx text-#D63C38 leading-[32rpx]">
              位置异常 请填写备注及拍照
            </view>
            <view class="reason-box">
              <up-textarea
                v-model="item.address_abnormal_info.abnormal_content"
                border="none"
                placeholder="请输入异常备注"
                count
                :disabled="item.status === CheckEnum.address_abnormal"
                maxlength="200"></up-textarea>
              <Upload
                v-model="item.address_abnormal_info.abnormal_attachment"
                :max-count="10"
                :multiple="true"
                :disabled="item.status === CheckEnum.address_abnormal"
                :action="action" />
            </view>
          </view>
          <view v-else-if="item.status === CheckEnum.abnormal" class="mt-24rpx">
            <up-button
              class="custom-button"
              :custom-style="{
                width: '152rpx',
                height: '56rpx',
                fontSize: '24rpx',
                lineHeight: '56rpx',
                margin: '0',
              }"
              text="处理异常"
              shape="circle"
              color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
              @click="onReissueRequest"></up-button>
          </view>
        </view>
      </template>
    </up-steps-item>
  </up-steps>
  <up-action-sheet
    :actions="sheetList"
    :show="show"
    :close-on-click-overlay="true"
    :close-on-click-action="true"
    cancel-text="取消"
    @close="onClose"
    @select="onSelectSheet"></up-action-sheet>
</template>

<script lang="ts" setup>
import { CheckEnum } from "@/api/enums/value";
import { action } from "@/config";
import { useActionSheet } from "../signIn/useActionSheet";

const props = defineProps({
  companyPosition: {
    type: String,
    default: "",
  },
  list: {
    type: Array as PropType<any[]>,
    default: [],
  },
  stepCurrent: {
    type: Number,
    default: 0,
  },
});

const icon = computed(() => {
  const iconObj = {
    [CheckEnum.abnormal]: "/static/images/ic_guanbi.png",
    [CheckEnum.address_abnormal]: "/static/images/ic_guanbi.png",
    [CheckEnum.checked]: "/static/images/ic_yiqian.png",
    [CheckEnum.un_check]: "/static/images/ic_weiqian.png",
    [CheckEnum.free]: "/static/images/ic_yiqian.png",
    [CheckEnum.leave]: "/static/images/ic_yiqian.png",
  };
  return (data = CheckEnum.checked) => {
    return iconObj?.[data];
  };
});

watch(
  () => props.list,
  (val) => {
    attendanceDate.value = val[0]?.words.split(" ")[1];
    abnormalTypeStr.value = val
      .filter((item) => item?.tags) // 过滤无 tags 的项
      .flatMap(
        (item) =>
          item.tags
            .map((tag) => tag?.tag_name) // 安全访问 tag_name
            .filter(Boolean), // 过滤空 tag_name
      )
      .reduce(
        (unique, tag) => (unique.includes(tag) ? unique : [...unique, tag]),
        [],
      )
      .join(",");
  },
);

const {
  abnormalTypeStr,
  attendanceDate,
  sheetList,
  show,
  onClose,
  onSelectSheet,
  onReissueRequest,
} = useActionSheet();
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-steps-item__content {
  margin-top: 0 !important;
}

::v-deep .u-steps-item__line--column {
  background: #d8d8d8 !important;
}

::v-deep .u-textarea {
  padding: 18rpx 0;
}

::v-deep .u-textarea--disabled {
  background: #fff;
}

.reason-box {
  padding: 18rpx 24rpx;
  border: 2rpx solid rgb(155 155 155 / 10%);
  border-radius: 24rpx;
}
</style>
