<template>
  <view class="center rounded-[24rpx] bg-#F9F9F9" @click="goDetail(item)">
    <view class="h-full px-24rpx">
      <view class="mb-8rpx text-36rpx text-#616161 font-500 leading-[40rpx]">
        {{ item.salary_month }}
      </view>
      <view class="text-26rpx text-#616161 font-400 leading-[40rpx]">
        {{ item.salary_year }}
      </view>
    </view>
    <view
      class="flex flex-1 items-center justify-between bg-white px-24rpx py-40rpx">
      <view class="flex flex-col items-start gap-y-24rpx">
        <view class="flex items-center">
          <view class="round" :style="{ backgroundColor: color }"></view>
          <view>{{ item.company_name }}</view>
        </view>
        <view class="center">
          <view class="round" :style="{ backgroundColor: color }"></view>
          <view>{{ item.position_name }}</view>
        </view>
        <view class="center">
          <view class="round" :style="{ backgroundColor: color }"></view>
          <view>{{ item.work_hours }}小时</view>
        </view>
      </view>
      <view
        v-if="item.is_confirmed"
        class="w-180rpx center font-500"
        :style="{ color }">
        <view class="self-end text-28rpx leading-[30rpx]">¥</view>
        <view class="text-40rpx leading-[40rpx]">{{ item.salary }}</view>
      </view>
      <view v-else class="text-28rpx leading-[40rpx]" :style="{ color }">
        待提交
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object as PropType<any>,
    default: () => {},
  },
});

const goDetail = (item) => {
  console.log("goDetail===", item);

  uni.navigateTo({
    url: `/pages/active/paySlip/detail?studentSalaryId=${props.item.id}`,
  });
};

const color = computed(() => {
  return props.item?.is_confirmed ? "#D63C38" : "#FFA769";
});
</script>

<style lang="scss" scoped>
.round {
  @apply h-14rpx w-14rpx rounded-[50%] mr-20rpx;
}
</style>
