<template>
  <ItemBox
    title="审批流程"
    sub-title="已由管理员预设不可修改审批人和删除抄送人">
    <view class="mt-32rpx">
      <up-steps :current="current" direction="column">
        <up-steps-item v-for="(item, index) in list" :key="index">
          <template #icon>
            <view
              class="label-box relative text-center text-28rpx text-white font-500 leading-[56rpx]">
              {{ item?.name[0] }}
              <image
                class="absolute bottom--4rpx right--8rpx h-24rpx w-24rpx"
                :src="icon(item.status)"
                mode="scaleToFill" />
            </view>
          </template>
          <template #title>
            <view
              class="flex-1 text-24rpx text-#212121 font-400 leading-[24rpx]">
              <view class="mb-8rpx font-500">
                {{ item.name }}{{ item.tag ? `（${item.tag}）` : "" }}
              </view>
              <view class="flex items-center justify-between text-#9B9B9B">
                <!-- <view>XXX学生</view> -->
                <view>{{ statusText(item.status) }}{{ item.time }}</view>
              </view>
            </view>
          </template>
          <template #desc>
            <view
              :class="[index !== list.length - 1 && !item.desc && 'h-104rpx']">
              <view
                v-if="item.audit_remark"
                :class="index === list.length - 1 && 'mb-0'"
                class="my-32rpx rounded-[24rpx] bg-#FFE7E6 px-24rpx py-32rpx text-24rpx text-#D63C38 font-400 leading-[40rpx]">
                驳回原因：{{ item.audit_remark }}
              </view>

              <view
                v-if="item.star_score"
                class="mt-32rpx rounded-24rpx bg-[#F5F6F8] p-32rpx">
                <up-rate
                  v-model="item.star_score"
                  :readonly="true"
                  active-color="#F19D64"
                  inactive-color="#DADCDF"
                  gutter="8"
                  size="25"></up-rate>
              </view>
            </view>
          </template>
        </up-steps-item>
      </up-steps>
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
import { StatusEnum } from "@/api/enums/value";

const props = defineProps({
  list: {
    type: Array as PropType<any[]>,
    default: [],
  },
});

const current = ref<number>();

const statusText = computed(() => {
  const textObj = {
    [StatusEnum.approved]: "已同意",
    [StatusEnum.pending]: "等待中",
    [StatusEnum.rejected]: "已驳回",
  };
  return (data) => {
    return textObj?.[data];
  };
});

const icon = computed(() => {
  const iconObj = {
    [StatusEnum.approved]: "/static/images/ic_tongguo.png",
    [StatusEnum.pending]: "/static/images/ic_shenpizhong.png",
    [StatusEnum.rejected]: "/static/images/ic_bohui.png",
  };
  return (data = StatusEnum.approved) => {
    return iconObj?.[data];
  };
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-steps-item__wrapper {
  width: 56rpx;
  height: 56rpx;
}

::v-deep .u-steps-item__content {
  margin-top: 4rpx !important;
  margin-left: 24rpx !important;
}
// ::v-deep .u-steps-item__content__desc {
//   height: 104rpx;
// }

::v-deep .u-steps-item__line {
  left: 28rpx;
}

.label-box {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(272deg, #d63c38 3%, #e76b67 100%);
  border-radius: 8rpx;
}
</style>
