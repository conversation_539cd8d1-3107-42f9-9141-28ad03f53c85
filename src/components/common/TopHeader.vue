<template>
  <view
    relative
    flex
    items-center
    justify-between
    bg-white
    px-30rpx
    pb-20rpx
    pt-26rpx>
    <view flex items-baseline @click="showModal('title')">
      <view class="mr-8rpx h-42rpx text-30rpx text-#212121 leading-[42rpx]">
        {{ formInfo.title }}
      </view>
      <image
        class="h-10rpx w-10rpx translate-y-[2rpx] transform"
        src="/static/images/ic_xiala.png" />
    </view>
    <view
      v-if="isShowSearch"
      center
      rounded-12rpx
      px-16rpx
      py-12rpx
      class="bg-[#F6F6F6]"
      @click="toSearch">
      <up-image
        src="/static/images/ic_shaixuan.png"
        width="28rpx"
        height="28rpx" />
      <view class="ml-10rpx text-26rpx text-[#616161] font-400 leading-[36rpx]">
        {{ searchName }}
      </view>
    </view>
  </view>
  <up-popup v-model:show="show" closeable round="24rpx">
    <view>
      <view
        class="text-#212121 leading-[42rpx]"
        mt-30rpx
        text-center
        text-32rpx
        font-400>
        筛选
      </view>
      <view p-30rpx pt-48rpx>
        <RadioBox :list="list" />
        <view class="action-buttons">
          <u-button
            :custom-style="customStyle"
            shape="circle"
            color="#F6F6F6"
            @click="resetSelection">
            重置
          </u-button>
          <u-button
            shape="circle"
            :custom-style="{ flex: 1 }"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="confirmSelection">
            确认
          </u-button>
        </view>
      </view>
    </view>
  </up-popup>

  <up-picker
    :show="showPicker"
    :columns="columns"
    confirm-color="#D63C38"
    key-name="name"
    @cancel="onCancel"
    @confirm="onSure"></up-picker>
</template>

<script lang="ts" setup>
import type { StudentInternshipListRes } from "@/api/internshipProgram/types";
import type { RadioType } from "./type";
import { InternshipProgramApi } from "@/api";
import usePicker from "@/hooks/usePicker";
import useApplicationStore from "@/store/modules/application";

const props = defineProps({
  isShowSearch: {
    type: Boolean,
    default: true,
  },
  optionList: {
    type: Array as PropType<RadioType[]>,
    default: () => {
      return [
        {
          title: "状态",
          selectedValue: "",
          list: [
            {
              name: "全部",
              value: "all",
            },
            {
              name: "审核通过",
              value: "pending",
            },
            {
              name: "审核中",
              value: "approved",
            },
            {
              name: "已驳回",
              value: "revoked",
            },
            {
              name: "已撤销",
              value: "rejected",
            },
          ],
        },
        {
          title: "类型",
          selectedValue: "",
          list: [
            {
              name: "全部",
              value: "all",
            },
            {
              name: "实习申请",
              value: "pending",
            },
            {
              name: "企业变更",
              value: "approved",
            },
            {
              name: "岗位变更",
              value: "rejected",
            },
            {
              name: "实习结束",
              value: "rejected",
            },
          ],
        },
      ];
    },
  },
});
const emit = defineEmits(["change"]);

const list = ref<RadioType[]>([]);

onMounted(() => {
  list.value = props.optionList;
  getStudentInternshipList();
});

const { setCurrentPlanInfo } = useApplicationStore();
const { currentPlanInfo } = storeToRefs(useApplicationStore());
const getStudentInternshipList = async () => {
  try {
    const data = await InternshipProgramApi.getStudentInternshipList();
    console.log("getStudentInternshipList List:", data);
    columns.value[0] = data;
    if (currentPlanInfo.value.id) {
      formInfo.value.title = currentPlanInfo.value.name as string;
      return;
    }

    setCurrentPlanInfo(data[0]);
    if (data.length) {
      formInfo.value.title = data[0].name as string;
    }
  } catch (error) {
    console.error("Error fetching internship list:", error);
  }
};

const show = ref(false);
const toSearch = () => {
  show.value = true;
};

const customStyle = reactive({
  color: "#616161",
  width: "260rpx",
  marginRight: "24rpx",
});

// 重置选择
const resetSelection = () => {
  list.value.forEach((item) => {
    item.selectedValue = "";
  });
};

const { searchValueList } = storeToRefs(useApplicationStore());
// 确认选择
const searchName = ref("筛选");
const confirmSelection = () => {
  show.value = false;
  searchName.value = searchValueList.value
    .flat()
    .map((item) => item.name)
    .join("、");
  emit("change");
};

const formInfo = ref({
  title: "选择实习计划",
});
const onSure = (e) => {
  onConfirm(e);
  setCurrentPlanInfo(e.value[0]);
  emit("change", e.value[0]);
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<StudentInternshipListRes[][]>([]);
</script>

<style lang="scss" scoped>
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 84rpx;
}
</style>
