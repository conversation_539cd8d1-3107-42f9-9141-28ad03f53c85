<template>
  <view class="relative rounded-[24rpx] bg-white px-24rpx py-32rpx">
    <view mb-24rpx flex items-center>
      <image
        v-if="isShowImage"
        :src="userStore.avatar || '/static/images/ic_touxiang.png'"
        mode="scaleToFill"
        class="mr-16rpx block h-48rpx w-48rpx border-rd-[50%]" />
      <view class="text-32rpx text-[#212121] font-500 leading-[40rpx]">
        {{ title }}
      </view>
    </view>
    <up-line color="#D8D8D8" dashed></up-line>
    <view
      v-if="isShowTitle"
      class="text-28rpx text-#212121 font-500 leading-[40rpx]"
      mt-24rpx>
      {{ subTitle }}
    </view>
    <view mt-24rpx flex flex-col gap-y-32rpx>
      <view v-for="(subItem, subIndex) in initData" :key="subIndex">
        <view v-if="subItem.type === 'file'">
          <view
            v-if="subItem.label && item[subItem.key]?.length"
            class="mb-24rpx mr-10rpx text-#616161">
            {{ subItem.label }}
          </view>
          <UploadList :file-list="item[subItem.key]" :mode="ModeEnum.DETAIL" />
        </view>
        <view v-else-if="subItem.type === 'textarea'">
          <view class="mb-24rpx mr-10rpx text-#616161">
            {{ subItem.label }}
          </view>
          <view class="text-#212121">{{ item[subItem.key] }}</view>
        </view>
        <view v-else-if="subItem.type === 'table'">
          <view v-if="subItem.label" class="mb-24rpx mr-10rpx text-#616161">
            {{ subItem.label }}
          </view>
          <Table
            :columns="subItem.columns"
            :data="item[subItem.key]"
            :border="false"
            first-col-color="#616161" />
        </view>
        <view v-else-if="subItem.type === 'slot'">
          <view v-if="subItem.label" class="mb-24rpx mr-10rpx text-#616161">
            {{ subItem.label }}
          </view>
          <slot :name="subItem.slotName" />
        </view>
        <view v-else flex items-center justify-between>
          <view class="mr-10rpx text-#616161">{{ subItem.label }}</view>
          <view v-if="subItem.key" class="text-#212121">
            {{ item[subItem.key] }}
          </view>
        </view>
      </view>
    </view>
    <view v-if="item.fileList?.length" class="mt-24rpx">
      <UploadList :file-list="item.fileList" :mode="ModeEnum.DETAIL" />
    </view>

    <image
      v-if="isShowTag"
      absolute
      right-16rpx
      top-32rpx
      block
      h-140rpx
      w-140rpx
      :src="statusImg[item.audit_status]"
      mode="scaleToFill" />
  </view>
</template>

<script lang="ts" setup>
import {
  ApplyEnum,
  FeedbackEnum,
  ModeEnum,
  ReadOverEnum,
  StatusEnum,
} from "@/api/enums/value";
import { useUserStore } from "@/store";

const props = defineProps({
  // 数据源
  item: {
    type: Object as PropType<any>,
    default: () => {},
  },
  // 展示项
  initData: {
    type: Array as PropType<any[]>,
    default: [],
  },
  // 是否展示头像
  isShowImage: {
    type: Boolean,
    default: true,
  },
  isShowTitle: {
    type: Boolean,
    default: true,
  },
  isShowTag: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "申请",
  },
  subTitle: {
    type: String,
    default: "申请详情",
  },
  type: {
    type: String,
    default: ApplyEnum.audit,
  },
});
const emit = defineEmits(["finish"]);

const userStore = useUserStore();

const statusImg = computed(() => {
  if (props.type === ApplyEnum.audit) {
    return {
      [StatusEnum.approved]: "/static/images/status/img_yitongguo.png",
      [StatusEnum.pending]: "/static/images/status/img_shenhezhong.png",
      [StatusEnum.rejected]: "/static/images/status/img_yibohui.png",
      [StatusEnum.revoked]: "/static/images/status/img_yichexiao.png",
    };
  } else if (props.type === ApplyEnum.readOver) {
    return {
      [ReadOverEnum.pending]: "/static/images/status/ic_piyuezhong.png",
      [ReadOverEnum.confirmed]: "/static/images/status/ic_yipiyue.png",
      [ReadOverEnum.rejected]: "/static/images/status/img_yibohui.png",
      [ReadOverEnum.revoked]: "/static/images/status/img_yichexiao.png",
    };
  } else if (props.type === ApplyEnum.feedback) {
    return {
      [FeedbackEnum.confirmed]: "/static/images/status/ic_yitijiao.png",
      [FeedbackEnum.revoked]: "/static/images/status/img_yichexiao.png",
    };
  } else {
    return {
      [StatusEnum.END]: "/static/images/status/ic_yijieshu.png",
      [StatusEnum.REPORT]: "/static/images/status/ic_yishangbao.png",
    };
  }
});

// const statusImg = reactive({
//   [StatusEnum.approved]: "/static/images/status/img_yitongguo.png",
//   [StatusEnum.pending]: "/static/images/status/img_shenhezhong.png",
//   [StatusEnum.rejected]: "/static/images/status/img_yibohui.png",
//   [StatusEnum.revoked]: "/static/images/status/img_yichexiao.png",

//   [StatusEnum.SUBMITTED]: "/static/images/status/ic_yitijiao.png",

//   [StatusEnum.END]: "/static/images/status/ic_yijieshu.png",
//   [StatusEnum.REPORT]: "/static/images/status/ic_yishangbao.png",

//   [StatusEnum.REVIEWED]: "/static/images/status/ic_yipiyue.png",
//   [StatusEnum.UNDERREVIEW]: "/static/images/status/ic_piyuezhong.png",
// });
</script>

<style lang="scss" scoped></style>
