<template>
  <view class="file-list flex flex-col gap-y-24rpx">
    <view
      v-for="(file, index) in fileList"
      :key="index"
      class="relative flex items-center">
      <image
        class="mr-24rpx block h-80rpx w-80rpx"
        :src="getFileIcon(file)"
        mode="scaleToFill" />
      <view flex-1>
        <view class="text-28rpx text-#3d3d3d leading-40rpx">
          {{ file.name }}
        </view>
        <view mt-8rpx between>
          <view class="text-24rpx text-#9b9b9b leading-34rpx">
            {{ formatFileSize(file.size) }}
          </view>
          <image
            class="block h-32rpx w-32rpx"
            src="/static/images/upload/ic_more.png"
            mode="scaleToFill"
            @tap="showActionSheet(file)" />
        </view>
      </view>
      <view
        v-if="file.status === 'uploading'"
        class="bg-color"
        absolute
        bottom-0
        left-0
        right-0
        top-0
        h-full
        w-full
        center
        @tap="() => {}">
        <up-loading-icon mode="circle"></up-loading-icon>
      </view>
    </view>
  </view>
  <up-action-sheet
    :actions="list"
    :show="show"
    :close-on-click-overlay="true"
    :close-on-click-action="true"
    cancel-text="取消"
    @close="onClose"
    @select="onSelectSheet"></up-action-sheet>
</template>

<script lang="ts" setup>
import type { UploadFile } from "./type";
import { FileActionEnum, ModeEnum } from "@/api/enums/value";
import { formatFileSize } from "@/utils/file";

const props = defineProps({
  mode: {
    type: String,
    default: ModeEnum.ADD,
  },
  fileList: {
    type: Array as PropType<any[]>,
    default: [],
  },
});

const emit = defineEmits(["delete"]);

const list = ref([
  { name: "预览", type: "preview" },
  { name: "删除", type: "delete", disabled: props.mode === ModeEnum.DETAIL },
]);
const show = ref(false);
const currentFile = ref<UploadFile>();
const onClose = () => {
  console.log("onClose");
  show.value = false;
};
const onSelectSheet = (curItem) => {
  console.log("onSelectSheet===", curItem);
  if (curItem.type === FileActionEnum.preview) {
    previewFile(currentFile.value as UploadFile);
  } else if (curItem.type === FileActionEnum.delete) {
    emit("delete", currentFile.value);
  }
};
const showActionSheet = (file: UploadFile) => {
  console.log("showActionSheet", file);
  currentFile.value = file;
  show.value = true;
};

// 获取文件图标
const getFileIcon = (file: UploadFile): string => {
  const extension = file.name
    ?.substring(file.name.lastIndexOf(".") + 1)
    .toLowerCase();

  switch (extension) {
    case "pdf":
      return "/static/images/upload/ic_pdf.png";
    case "doc":
    case "docx":
      return "/static/images/upload/ic_word.png";
    default:
      return "/static/images/upload/ic_image.png";
  }
};

// 预览文件
const previewFile = (file: UploadFile) => {
  console.log("previewFile", file);
  const extension = file.url
    ?.substring(file.url.lastIndexOf(".") + 1)
    .toLowerCase() as string;
  console.log("extension", extension);

  // 图片预览
  if (file?.type?.includes("image")) {
    previewImage(file);
    return;
  }

  // 视频预览
  if (["mp4", "mov", "avi", "flv"].includes(extension)) {
    uni.navigateTo({
      url: `/pages/common/video-player?url=${encodeURIComponent(file.url)}`,
    });
    return;
  }

  // 文件预览，仅支持特定平台
  if (
    ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf"].includes(extension)
  ) {
    // 小程序特有API，打开文档
    console.log("file.url", file.url);
    uni.downloadFile({
      url: file.url,
      success(res) {
        const filePath = res.tempFilePath;
        uni.openDocument({
          filePath,
          showMenu: true,
          success: () => {
            console.log("打开文档成功");
          },
          fail: () => {
            uni.showToast({
              title: "无法打开此类型文件",
              icon: "none",
            });
          },
        });
      },
    });

    return;
  }

  // 其他类型文件暂不支持预览
  uni.showToast({
    title: "该文件类型暂不支持预览",
    icon: "none",
  });
};

// 预览图片
const previewImage = (file: UploadFile) => {
  // 获取当前所有图片的url
  const urls = props.fileList
    .filter((item) => item?.type?.includes("image"))
    .map((item) => item.url);
  // 打开预览
  uni.previewImage({
    urls,
    current: file.url,
  });
};
</script>

<style lang="scss" scoped>
.bg-color {
  background: rgb(0 0 0 / 50%);
}
</style>
