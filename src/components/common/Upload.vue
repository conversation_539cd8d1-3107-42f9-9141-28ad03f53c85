<template>
  <up-upload
    :max-count="maxCount"
    :accept="accept"
    :custom-style="customStyle"
    :deletable="deletable"
    :disabled="disabled"
    :multiple="multiple"
    :preview-full-image="previewFullImage"
    @after-read="handleAfterRead"
    @delete="handleDelete">
    <slot>
      <image
        class="block h-36rpx w-36rpx"
        src="/static/images/upload/ic_add.png"
        mode="scaleToFill" />
    </slot>
  </up-upload>
</template>

<script setup lang="ts">
import type {
  UploadComplete,
  UploadFile,
  UploadProps,
  UploadState,
  UploadStatus,
  UploadSuccess,
} from "./type";
import { preFixAction } from "@/config";
import { getToken, TokenPrefix } from "@/utils";
import { mpUploadFile } from "@/utils/request/upload";

const props = withDefaults(defineProps<UploadProps>(), {
  modelValue: undefined,
  maxCount: 10,
  action: "",
  accept: "all",
  acceptExtensions: () => ["doc", "docx", "pdf", "png", "jpg"],
  multiple: false,
  maxSize: 5,
  header: () => ({}),
  formData: () => ({}),
  deletable: true,
  disabled: false,
  autoUpload: true,
  previewFullImage: false,
  customStyle: () => ({}),
  showProgress: true,
  concurrent: 3,
});

const emit = defineEmits<{
  "update:modelValue": [value: UploadFile[]];
  afterRead: [event: any];
  success: [data: UploadSuccess];
  error: [message: string];
  delete: [event: any];
  complete: [data: UploadComplete];
}>();

// 使用计算属性来处理双向绑定
const fileList = computed({
  get: () => props.modelValue || [],
  set: (value) => {
    emit("update:modelValue", value);
  },
});

const uploadQueue = ref<UploadFile[]>([]);
const uploading = ref(false);
const uploadState = reactive<UploadState>({
  uploadedCount: 0,
  failedCount: 0,
  totalCount: 0,
});

// 检查文件类型是否允许
const isFileTypeAllowed = (fileName: string): boolean => {
  if (!props.acceptExtensions?.length) return true;

  const extension = fileName
    .substring(fileName.lastIndexOf(".") + 1)
    .toLowerCase();
  return props.acceptExtensions.some((ext) => {
    // 移除可能的点号
    const cleanExt = ext.startsWith(".") ? ext.substring(1) : ext;
    return cleanExt.toLowerCase() === extension;
  });
};

// 处理文件选择
const handleAfterRead = (event: any): void => {
  const files: UploadFile[] = event.file.length ? event.file : [event.file];
  console.log("handleAfterRead", files);
  const validFiles = files.filter((file) => {
    if (file.size && file.size > props.maxSize * 1024 * 1024) {
      handleError(`文件 ${file.name} 大小不能超过${props.maxSize}MB`);
      return false;
    }

    if (props.accept === "image" && file.type && !file.type.includes("image")) {
      handleError(`文件 ${file.name} 不是图片格式`);
      return false;
    }

    if (props.accept === "video" && file.type && !file.type.includes("video")) {
      handleError(`文件 ${file.name} 不是视频格式`);
      return false;
    }

    const invalidFiles = files.filter(
      (file) => !isFileTypeAllowed(file.name || ""),
    );
    if (invalidFiles.length > 0) {
      uni.showToast({
        title: "文件类型不允许",
        icon: "none",
      });
      return;
    }

    return true;
  });

  if (validFiles.length !== files.length) return;

  // 直接更新 fileList
  const newFiles = validFiles.map((file) => ({
    ...file,
    status: "uploading" as UploadStatus,
    message: "上传中",
  }));

  // 使用响应式更新
  if (props.maxCount === 1) {
    fileList.value = [...newFiles];
  } else {
    const updatedList = [...fileList.value, ...newFiles];
    fileList.value = updatedList;
  }

  uploadQueue.value.push(...validFiles);
  uploadState.totalCount = uploadQueue.value.length;

  if (props.autoUpload && files.length > 0) {
    uploadFile(validFiles);
  }

  emit("afterRead", validFiles);
};

// 上传单个文件
const uploadFile = async (files: UploadFile[]): Promise<void> => {
  try {
    const list = files.map((item) => ({
      uri: item.url,
    }));

    if (!props.action) {
      uni.showToast({
        title: "未设置上传地址",
        icon: "none",
      });
      return;
    }
    let baseURL = props.action;
    // #ifdef H5
    if (import.meta.env.VITE_APP_PROXY === "true") {
      baseURL = preFixAction;
    }
    // #endif
    const res = await mpUploadFile(list, {
      url: baseURL,
      name: "upload_file",
      header: {
        Authorization: `${TokenPrefix}${getToken()}`,
      },
      formData: {
        fileName: "filen",
      },
    });

    // 更新文件状态
    const updatedList = fileList.value.map((file) => {
      if (file.status === "uploading") {
        return {
          ...file,
          status: "success" as UploadStatus,
          message: "上传成功",
          url: res.data,
        };
      }
      return file;
    });
    fileList.value = updatedList;
  } catch (err: any) {
    console.log(err);
    // 更新文件状态为失败
    // const updatedList = fileList.value.map((file) => {
    //   if (file.status === "uploading") {
    //     return {
    //       ...file,
    //       status: "error" as UploadStatus,
    //       message: "上传失败",
    //     };
    //   }
    //   return file;
    // });
    const updatedList = fileList.value.filter(
      (file) => file.status !== "uploading",
    );
    fileList.value = updatedList;
    uni.showToast({
      title: err?.error,
      icon: "none",
    });
  }
};

// 删除文件
const handleDelete = (event: any): void => {
  const { index } = event;
  const file = fileList.value[index];
  fileList.value.splice(index, 1);
  emit("delete", { file, fileList: fileList.value });
};

// 开始上传队列
const startUpload = async (): Promise<void> => {
  if (uploading.value) return;
  uploading.value = true;

  try {
    while (uploadQueue.value.length > 0) {
      const tasks = uploadQueue.value
        .splice(0, props.concurrent)
        .map((file) => uploadFile([file]));
      await Promise.all(tasks);
    }
  } finally {
    uploading.value = false;

    if (uploadState.failedCount === 0) {
      emit("complete", {
        success: true,
        message: "全部上传成功",
      });
    } else {
      emit("complete", {
        success: false,
        message: `上传完成，失败 ${uploadState.failedCount} 个文件`,
      });
    }
  }
};

// 错误提示
const handleError = (message: string): void => {
  uni.showToast({
    title: message,
    icon: "none",
  });
  emit("error", message);
};

// 对外暴露的方法
const submit = (): void => {
  const pendingFiles =
    fileList.value?.filter((file) => file.status !== "success") || [];
  uploadQueue.value.push(...pendingFiles);
  uploadState.totalCount = uploadQueue.value.length;
  startUpload();
};

const retry = (): void => {
  const failedFiles =
    fileList.value?.filter((file) => file.status === "error") || [];
  uploadQueue.value.push(...failedFiles);
  uploadState.totalCount = uploadQueue.value.length;
  startUpload();
};

const clear = (): void => {
  fileList.value = [];
  uploadQueue.value = [];
  uploadState.uploadedCount = 0;
  uploadState.failedCount = 0;
  uploadState.totalCount = 0;
};

// 暴露给父组件的方法
defineExpose({
  submit,
  retry,
  clear,
});
</script>

<style lang="scss" scoped></style>
