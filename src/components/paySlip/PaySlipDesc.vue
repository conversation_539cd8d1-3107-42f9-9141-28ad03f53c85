<template>
  <view
    class="flex flex-col items-center rounded-[24rpx] bg-white px-24rpx pb-40rpx pt-60rpx">
    <image
      class="block h-96rpx w-96rpx"
      src="/static/images/ic_gongzi.png"
      mode="scaleToFill" />
    <view
      class="mb-32rpx ml-60rpx mt-48rpx w-180rpx center text-#D63C38 font-600">
      <view class="self-end text-36rpx leading-[50rpx]">¥</view>
      <up-input
        v-model="formInfo.salary"
        font-size="40rpx"
        color="#D63C38"
        disabled-color="#ffffff"
        placeholder="请填写"
        border="none"></up-input>
      <!-- <view class="text-40rpx leading-[40rpx]">{{ formInfo.salary }}</view> -->
    </view>
    <view class="text-32rpx text-#212121 font-400 leading-[40rpx]">
      实习工资
    </view>
    <view w-full>
      <up-form
        ref="formRef"
        label-position="left"
        :model="formInfo"
        :rules="rules"
        label-width="auto">
        <up-form-item label="实习单位" prop="company_name">
          <up-input
            v-model="formInfo.company_name"
            disabled-color="#ffffff"
            placeholder="请填写"
            input-align="right"
            disabled
            border="none"></up-input>
        </up-form-item>
        <up-form-item label="实习岗位" prop="position_name">
          <up-input
            v-model="formInfo.position_name"
            disabled-color="#ffffff"
            placeholder="请填写"
            input-align="right"
            disabled
            border="none"></up-input>
        </up-form-item>
        <up-form-item label="工时" prop="work_hours">
          <up-input
            v-model="formInfo.work_hours"
            disabled-color="#ffffff"
            placeholder="请填写(非必填)"
            input-align="right"
            border="none"></up-input>
        </up-form-item>
      </up-form>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { IPaySlipDesc } from "./type";

const formRef = ref<any>();
const formInfo = defineModel<IPaySlipDesc>({
  default: () => ({
    salary: "",
    company_name: "",
    position_name: "",
    work_hours: "",
  }),
});
const rules = ref({
  company_name: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
  position_name: {
    type: "string",
    required: true,
    message: "请填写",
    trigger: ["blur", "change"],
  },
});
</script>

<style lang="scss" scoped></style>
