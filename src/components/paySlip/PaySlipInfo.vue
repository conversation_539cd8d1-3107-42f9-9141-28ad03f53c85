<template>
  <ItemBox title="工资单信息">
    <template #titleRight>
      <Upload
        v-model="formInfo.attachment"
        :max-count="10"
        :disabled="disabled"
        :action="action" />
    </template>
    <view v-if="formInfo.attachment?.length" class="mt-24rpx">
      <UploadList
        :file-list="formInfo.attachment"
        @delete="(val) => onDelete(val, 'attachment')" />
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { IPaySlipInfo } from "./type";
import { action } from "@/config";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const formInfo = defineModel<IPaySlipInfo>({
  default: () => ({
    attachment: [],
  }),
});

const onDelete = (file, key) => {
  formInfo.value[key] = formInfo.value?.[key]?.filter((item) => item !== file);
};
</script>

<style lang="scss" scoped></style>
