<template>
  <view class="p-24rpx">
    <view v-for="(item, index) in list" :key="index">
      <CardItem
        :item="item"
        :init-data="initData"
        :is-show-image="false"
        :type="ApplyEnum.feedback"
        :title="`${userStore.name}的实习反馈`">
        <view flex justify-end gap-x-16rpx>
          <up-button
            v-if="item.audit_status === FeedbackEnum.confirmed"
            :custom-style="{
              border: '1px solid #9B9B9B',
              color: '#9B9B9B',
              width: '155rpx',
              height: '56rpx',
            }"
            text="撤销反馈"
            shape="circle"
            @click="onRevoke(item)"></up-button>
          <up-button
            v-else-if="item.audit_status === FeedbackEnum.revoked"
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="onResubmit(item)"></up-button>
          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="详情"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </view>
      </CardItem>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { FeedbackApi } from "@/api";
import { ApplyEnum, FeedbackEnum, ModeEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

// 获取反馈列表
const getFeedbackList = async () => {
  try {
    showLoading();
    const data = await FeedbackApi.getFeedbackList({
      internshipPlanId: props.planId,
    });
    list.value = data.map((item) => {
      return {
        ...item,
        audit_status: item.status,
      };
    });
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const init = debounce(async () => {
  try {
    getFeedbackList();
  } catch (err) {
    console.log(err);
  }
}, 300);

watch(
  () => props.planId,
  () => {
    init();
  },
);

onShow(() => {
  if (props.planId) {
    init();
  }
});

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});
const list = ref<any>([]);
const initData = ref([
  {
    label: "反馈内容",
    key: "content",
  },
  {
    label: "反馈时间",
    key: "submit_date",
  },
]);

const { showModal } = useModal();
const onRevoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await FeedbackApi.revokeFeedback({
        feedbackId: item.id,
      });
      Toast("撤销成功");
      getFeedbackList();
    }
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
const onResubmit = (item) => {
  uni.navigateTo({
    url: `/pages/active/realTimeFeedback/index?mode=${ModeEnum.EDIT}&feedbackId=${item.id}`,
  });
};
const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/realTimeFeedback/detail?feedbackId=${item.id}`,
  });
};

defineExpose({
  getFeedbackList,
});
</script>

<style lang="scss" scoped></style>
