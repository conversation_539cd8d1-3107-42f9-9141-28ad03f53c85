<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <LeaveApplyInfo ref="leaveApplyInfoRef" v-model="leaveApplyInfoData" />
    <LeaveReason ref="leaveReasonRef" v-model="leaveReasonData" />
    <ExplanationAttachment
      ref="explanationAttachmentRef"
      v-model="explanationAttachmentData" />
    <ApprovalProcess ref="approvalProcessRef" v-model="approvalProcessData" />
    <up-button
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onSubmit"></up-button>
  </view>
</template>

<script lang="ts" setup>
import type { GetApprovalTeacherRes } from "@/api/askLeave/types";
import type { IExplanationAttachment } from "../signIn/type";
import type { ILeaveApplyInfo, ILeaveReason } from "./type";
import { AskLeaveApi } from "@/api";
import { enumApi } from "@/api/enums";
import { useLoading } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
  leaveAttendanceId: {
    type: Number,
    default: 0,
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

// 获取请假记录详情
const getLeaveAttendanceDetail = async () => {
  try {
    showLoading();
    const data = await AskLeaveApi.getLeaveAttendanceDetail({
      leaveAttendanceId: props.leaveAttendanceId,
    });
    const { leave_type, leave_start, leave_end, leave_days, leave_reason } =
      data;
    Object.assign(leaveApplyInfoData.value, {
      leave_type,
      leave_start,
      leave_end,
      leave_days,
    });

    leaveApplyInfoData.value.leaveTypeLabel = enumApi
      .get("LEAVETYPE")
      .findLabel(data.leave_type);
    Object.assign(leaveReasonData.value, {
      leave_reason,
    });
    explanationAttachmentData.value.attachment =
      data.attachment && JSON.parse(data.attachment);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

// 获取审批老师信息
const getApprovalTeacher = async () => {
  try {
    showLoading();
    const data = await AskLeaveApi.getApprovalTeacher({
      internshipPlanId: props.planId,
    });
    approvalProcessData.value = data;
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

onMounted(() => {
  // getApprovalTeacher();
  props.isEdit && getLeaveAttendanceDetail();
});

watch(
  () => props.planId,
  (val) => {
    val && getApprovalTeacher();
  },
  {
    immediate: true,
  },
);

const leaveApplyInfoRef = ref();
const leaveReasonRef = ref();
const approvalProcessRef = ref();

const leaveApplyInfoData = ref<ILeaveApplyInfo>({
  name: userStore.name || "",
  leave_type: "",
  leaveTypeLabel: "",
  leave_start: "",
  leave_end: "",
  leave_days: undefined,
});
const leaveReasonData = ref<ILeaveReason>({ leave_reason: "" });
const explanationAttachmentData = ref<IExplanationAttachment>({
  attachment: [],
});
const approvalProcessData = ref<GetApprovalTeacherRes>();

const onSubmit = () => {
  Promise.all([
    leaveApplyInfoRef.value?.formRef.validate(),
    leaveReasonRef.value?.formRef.validate(),
    approvalProcessRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      console.log("leaveApplyInfoData.valu", leaveApplyInfoData.value);
      console.log("leaveReasonData.valu", leaveReasonData.value);

      const allInfo: any = {
        ...leaveApplyInfoData.value,
        ...leaveReasonData.value,
        attachment: JSON.stringify(explanationAttachmentData.value.attachment),
        ...approvalProcessData.value,
      };
      console.log("All forms validated successfully", allInfo);
      if (props.isAdd) {
        await AskLeaveApi.submitLeaveAttendance(allInfo, {
          internshipPlanId: props.planId,
        });
        Toast("申请成功");
      } else {
        await AskLeaveApi.editLeaveAttendance(allInfo, {
          leaveAttendanceId: props.leaveAttendanceId,
        });
        Toast("修改成功");
      }
      uni.navigateBack();
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}

::v-deep .u-line {
  border-color: $u-border-color !important;
}
</style>
