<template>
  <view class="rounded-[24rpx] bg-white px-24rpx py-10rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="姓名"
        prop="name"
        required
        border-bottom
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.name"
          disabled
          disabled-color="#ffffff"
          placeholder="请输入姓名"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
      <up-form-item
        label="请假类型"
        prop="leaveTypeLabel"
        required
        border-bottom
        :custom-style="customStyle"
        @click="showModal('leaveTypeLabel')">
        <up-input
          v-model="formInfo.leaveTypeLabel"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="开始日期"
        prop="leave_start"
        required
        border-bottom
        :custom-style="customStyle"
        @click="openDate('leave_start')">
        <up-input
          v-model="formInfo.leave_start"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="结束日期"
        prop="leave_end"
        required
        border-bottom
        :custom-style="customStyle"
        @click="openDate('leave_end')">
        <up-input
          v-model="formInfo.leave_end"
          disabled
          disabled-color="#ffffff"
          style="pointer-events: none"
          placeholder="请选择"
          input-align="right"
          border="none"></up-input>
        <template #right>
          <view class="i-ep-arrow-right mb-4rpx ml-10rpx text-#9B9B9B"></view>
        </template>
      </up-form-item>
      <up-form-item
        label="时长"
        prop="leave_days"
        required
        :custom-style="customStyle">
        <up-input
          v-model="formInfo.leave_days"
          disabled
          disabled-color="#ffffff"
          placeholder="请输入时长"
          input-align="right"
          border="none"></up-input>
      </up-form-item>
    </up-form>

    <!-- 请假时间选择 -->
    <tPicker ref="tPickerRef" @confirm="handleConfirm"></tPicker>

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onSure" />
  </view>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import type { ILeaveApplyInfo } from "./type";
import { AskLeaveApi } from "@/api";
import { enumApi } from "@/api/enums";
import usePicker from "@/hooks/usePicker";

const tPickerRef = ref();
const currKey = ref<string>("");
const openDate = (key) => {
  currKey.value = key;
  tPickerRef.value.openPicker();
};
const handleConfirm = (value) => {
  console.log("handleConfirm1", value);
  formInfo.value[currKey.value] = value;
  if (formInfo.value.leave_start && formInfo.value.leave_end) {
    // const day = calculateWorkingDays(
    //   formInfo.value.leave_start,
    //   formInfo.value.leave_end,
    // );
    calLeaveDays();
  }
};

// 计算请假多少天
const calLeaveDays = async () => {
  const data = await AskLeaveApi.calLeaveDays({
    leave_start: formInfo.value.leave_start,
    leave_end: formInfo.value.leave_end,
  });
  formInfo.value.leave_days = data.leave_days;
  console.log("calLeaveDays===", data);
};

const customStyle = ref({ marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<ILeaveApplyInfo>({
  default: () => ({
    name: "",
    leave_type: "",
    leave_start: "",
    leave_end: "",
    leave_days: null,
  }),
});
const rules = ref({
  name: {
    type: "string",
    required: true,
    message: "请填写姓名",
    trigger: ["blur", "change"],
  },
  leaveTypeLabel: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  leave_start: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  leave_end: {
    type: "string",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
  leave_days: {
    type: "number",
    required: true,
    message: "请选择",
    trigger: ["blur", "change"],
  },
});

const onSure = (e) => {
  onConfirm(e);
  formInfo.value.leave_type = e.value[0]?.value;
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<IOption[][]>([enumApi.get("LEAVETYPE").options]);
defineExpose({
  formRef,
  formInfo,
});
</script>

<style lang="scss" scoped></style>
