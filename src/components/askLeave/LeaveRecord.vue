<template>
  <view class="p-24rpx">
    <view v-for="(item, index) in list" :key="index">
      <CardItem
        :item="item"
        :init-data="initData"
        :is-show-image="false"
        :title="`${userStore.name}的请假申请`">
        <view v-if="item.audit_status === StatusEnum.pending">
          <up-button
            :custom-style="{
              border: '1px solid #9B9B9B',
              color: '#9B9B9B',
              width: '155rpx',
              height: '56rpx',
            }"
            text="撤销申请"
            shape="circle"
            @click="onRevoke(item)"></up-button>
        </view>
        <view
          v-else-if="
            item.audit_status === StatusEnum.rejected ||
            item.audit_status === StatusEnum.revoked
          ">
          <up-button
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="onResubmit(item)"></up-button>
        </view>
        <view>
          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="详情"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </view>
      </CardItem>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { GetLeaveAttendanceRecordRes } from "@/api/askLeave/types";
import { AskLeaveApi } from "@/api";
import { enumApi } from "@/api/enums";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useModal } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";
import { debounce } from "lodash-es";

const props = defineProps({
  planId: {
    type: Number,
    default: 0,
  },
});

const userStore = useUserStore();

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});

// 获取请假记录
const getLeaveAttendanceRecord = async () => {
  const data = await AskLeaveApi.getLeaveAttendanceRecord({
    internshipPlanId: props.planId,
  });

  list.value = data.map((item) => {
    const startDay = enumApi.get("LEAVEDAY").findLabel(item.leave_start_day);
    const endDay = enumApi.get("LEAVEDAY").findLabel(item.leave_end_day);
    return {
      ...item,
      leave_type: enumApi.get("LEAVETYPE").findLabel(item.leave_type),
      leave_start: `${item.leave_start_date} ${startDay}`,
      leave_end: `${item.leave_end_date} ${endDay}`,
    };
  });
  console.log("getLeaveAttendanceRecord", data);
};

const init = debounce(async () => {
  if (props.planId) {
    getLeaveAttendanceRecord();
  }
}, 300);

onMounted(() => {
  init();
});

watch(
  () => props.planId,
  () => {
    init();
  },
);

const list = ref<GetLeaveAttendanceRecordRes[]>([]);
const initData = ref([
  {
    label: "请假类型",
    key: "leave_type",
  },
  {
    label: "开始日期",
    key: "leave_start",
  },
  {
    label: "结束日期",
    key: "leave_end",
  },
]);

const { showModal } = useModal();
const onRevoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      await AskLeaveApi.revokeLeaveAttendance({
        leaveAttendanceId: item.id,
      });
      Toast("撤销成功");
      getLeaveAttendanceRecord();
    }
  } catch (err) {
    console.log(err);
  }
};
const onResubmit = async (item) => {
  uni.navigateTo({
    url: `/pages/active/askLeave/index?mode=${ModeEnum.EDIT}&leaveAttendanceId=${item.id}`,
  });
};
const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/askLeave/detail?leaveAttendanceId=${item.id}`,
  });
};

defineExpose({
  init,
});
</script>

<style lang="scss" scoped></style>
