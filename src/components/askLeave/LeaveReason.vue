<template>
  <view class="rounded-[24rpx] bg-white p-24rpx">
    <up-form
      ref="formRef"
      label-position="left"
      :model="formInfo"
      :rules="rules"
      label-width="auto">
      <up-form-item
        label="请假事由"
        prop="leave_reason"
        required
        :custom-style="customStyle">
        <up-textarea
          v-model="formInfo.leave_reason"
          border="none"
          height="416rpx"
          :maxlength="200"
          placeholder="请输入请假事由"
          count></up-textarea>
      </up-form-item>
    </up-form>
  </view>
</template>

<script lang="ts" setup>
import type { ILeaveReason } from "./type";

const customStyle = ref({ display: "block", marginLeft: "24rpx" });
const formRef = ref<any>();
const formInfo = defineModel<ILeaveReason>({
  default: () => ({
    leave_reason: "",
  }),
});
const rules = ref({
  leave_reason: {
    type: "string",
    required: true,
    message: "请输入请假原因",
    trigger: ["change"],
  },
});

defineExpose({
  formRef,
  formInfo,
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-textarea {
  padding: 18rpx 0 32rpx;
}
</style>
