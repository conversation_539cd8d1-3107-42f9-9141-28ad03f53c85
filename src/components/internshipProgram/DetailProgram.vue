<template>
  <ItemBox title="实习计划">
    <view class="flex flex-col gap-y-32rpx pt-32rpx text-28rpx font-400">
      <view class="between gap-y-24rpx">
        <view class="mr-64rpx w-115rpx text-#616161 leading-[40rpx]">
          计划名称
        </view>
        <view class="value">{{ data.name }}</view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">编号</view>
        <view class="value">{{ data.code }}</view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">类型</view>
        <view class="value">
          {{ enumApi.get("TEMPLATE_TYPE_ENUMS").findLabel(data.type) }}
        </view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">起止时间</view>
        <view class="value">
          {{ `${data.plan_start_date} - ${data.plan_end_date}` }}
        </view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">实习目的</view>
        <view class="value">{{ data.purpose }}</view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">实习要求</view>
        <view class="value">{{ data.requirement }}</view>
      </view>
      <view class="flex justify-between gap-y-24rpx">
        <view class="label">实习内容</view>
        <view class="value">{{ data.content }}</view>
      </view>
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { Plan } from "@/api/internshipProgram/types";
import { enumApi } from "@/api/enums";

defineProps({
  data: {
    type: Object as PropType<Plan>,
    default: {},
  },
});
</script>

<style lang="scss" scoped>
.label {
  @apply mr-64rpx w-115rpx text-#616161 leading-[40rpx];
}

.value {
  @apply flex-1 text-right text-#212121 leading-[40rpx];
}
</style>
