<template>
  <ItemBox title="考核管理">
    <view class="flex flex-col gap-y-32rpx pt-32rpx text-28rpx font-400">
      <view class="row">
        <view>考核评价</view>
        <view class="w-146rpx">百分比</view>
      </view>
      <view class="row">
        <view class="text-#616161">实习前考核</view>
        <view class="w-146rpx">
          {{ data.before_internship_assessment_percentage || "-" }}%
        </view>
      </view>
      <view class="row">
        <view class="text-#616161">实习后考核</view>
        <view class="w-146rpx">
          {{ data.after_internship_assessment_percentage || "-" }}%
        </view>
      </view>
      <view class="row">
        <view class="text-#616161">企业教师评分</view>
        <view class="w-146rpx">
          {{ data.company_teacher_score_percentage || "-" }}%
        </view>
      </view>
      <view class="row">
        <view class="text-#616161">校内指导教师</view>
        <view class="w-146rpx">
          {{ data.internal_teacher_score_percentage || "-" }}%
        </view>
      </view>
      <view class="row">
        <view class="text-#616161">自我评价</view>
        <view class="w-146rpx">{{ data.self_score_percentage || "-" }}%</view>
      </view>
    </view>
  </ItemBox>
</template>

<script lang="ts" setup>
import type { Plan } from "@/api/internshipProgram/types";

defineProps({
  data: {
    type: Object as PropType<Plan>,
    default: {},
  },
});
</script>

<style lang="scss" scoped>
.row {
  &:nth-child(2n-1) {
    background: rgb(*********** / 10%);
    border-radius: 12rpx;
  }

  @apply between px-50rpx py-12rpx text-28rpx text-#212121 font-500;
}
</style>
