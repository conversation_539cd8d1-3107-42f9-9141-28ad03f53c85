<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <template v-if="messageList?.length">
      <view
        v-for="(item, index) in messageList"
        :key="index"
        @click="handleClick(item)">
        <MessageItem :item="item" />
      </view>
    </template>
    <view v-else mt-300rpx>
      <Empty />
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { GetMessageListRes } from "@/api/message/types";
import { MessageApi } from "@/api";
import { useLoading } from "@/hooks";

const { showLoading, hideLoading } = useLoading();

const messageList = ref<GetMessageListRes[]>();
const messageType = ref<string>("");
onLoad((options) => {
  console.log("onLoad===", options);
  messageType.value = options?.messageType;
});

onShow(async () => {
  console.log("tabbar page onShow");
  showLoading();
  await getMessageList();
  hideLoading();
});

const getMessageList = async () => {
  try {
    const data = await MessageApi.getMessageList({
      messageType: messageType.value,
    });
    messageList.value = data;
    console.log(data);
  } catch (err) {
    console.log(err);
  }
};

const markRead = async (messageId) => {
  try {
    const data = await MessageApi.markRead({
      messageId,
    });
    console.log(data);
  } catch (err) {
    console.log(err);
  }
};

const handleClick = async (item) => {
  try {
    showLoading();
    await markRead(item.id);
    const handle = {
      notify_announcement: () => {
        // uni.navigateTo({
        //   url: `/pages/tab/message/notice?messageType=${item.type}`,
        // });
      },
      internship_before_notify_student_apply_for_internship: () => {
        uni.navigateTo({
          url: `/pages/active/internshipApplication/index?messageType=${item.type}`,
        });
      },
      student_unsign_warning: () => {
        uni.navigateTo({
          url: `/pages/active/signIn/index?messageType=${item.type}`,
        });
      },
      student_address_abnormal_warning: () => {
        uni.navigateTo({
          url: `/pages/active/signIn/index?messageType=${item.type}`,
        });
      },
      reminder_teacher_guide: () => {
        uni.navigateTo({
          url: `/pages/active/myGrades/index?messageType=${item.type}`,
        });
      },
      attendance: () => {
        uni.navigateTo({
          url: `/pages/active/signIn/index?type=Replenish&messageType=${item.type}`,
        });
      },
      attendance_abnormal: () => {
        uni.navigateTo({
          url: `/pages/active/signIn/index?type=Replenish&messageType=${item.type}`,
        });
      },
      daily_report: () => {
        uni.navigateTo({
          url: `/pages/active/dailyReport/index?messageType=${item.type}`,
        });
      },
      monthly_report: () => {
        uni.navigateTo({
          url: `/pages/active/monthlyReport/index?messageType=${item.type}`,
        });
      },
      self_evaluation: () => {
        uni.navigateTo({
          url: `/pages/active/myGrades/index?messageType=${item.type}`,
        });
      },
      contact_teacher: () => {
        uni.navigateTo({
          url: `/pages/active/realTimeFeedback/index?messageType=${item.type}`,
        });
      },
      survey: () => {
        uni.navigateTo({
          url: `/pages/active/paySlip/index?messageType=${item.type}`,
        });
      },
      summary_report: () => {
        uni.navigateTo({
          url: `/pages/active/internshipSummary/index?messageType=${item.type}`,
        });
      },
      weekly_report: () => {
        uni.navigateTo({
          url: `/pages/active/weeklyReport/index?messageType=${item.type}`,
        });
      },
    };
    handle[item.service_type] && handle[item.service_type]();
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};
</script>

<style lang="scss" scoped></style>
