<template>
  <content-box title="消息">
    <view flex flex-col gap-y-50rpx p-24rpx pt-48rpx>
      <view
        v-for="(item, index) in list"
        :key="index"
        flex
        items-center
        @click="item.fun">
        <image
          mr-24rpx
          block
          h-96rpx
          w-96rpx
          :src="handleImg[item.type]"
          mode="scaleToFill" />
        <view flex-1>
          <view mb-8rpx between>
            <view class="text-32rpx text-#212121 font-500 leading-44rpx">
              {{ item.name }}
            </view>
            <view class="text-24rpx text-#ccc font-400 leading-32rpx">
              {{ item.last_one_datetime }}
            </view>
          </view>
          <view between>
            <view
              class="truncate text-26rpx text-#9B9B9B font-400 leading-32rpx">
              {{ item.description }}
            </view>
            <view
              v-if="item.count"
              class="h-16rpx w-16rpx rounded-full bg-#FF3D44"></view>
          </view>
        </view>
      </view>
    </view>
  </content-box>
</template>

<script setup lang="ts">
import type { GetMessageStatisticsRes } from "@/api/message/types";
import { MessageApi } from "@/api";
import { useLoading, usePermission } from "@/hooks";
import { useUserStore } from "@/store";
import { setToken } from "@/utils/auth";

interface IGetMessageStatisticsRes extends GetMessageStatisticsRes {
  fun: () => void;
}

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();
const token = ref("");
onMounted(async () => {
  if (token.value) {
    setToken(token.value);
    await userStore.info();
  }
});
onLoad((options) => {
  console.log("接收到的参数:", options);
  token.value = options?.token;
});

// 登录鉴权，微信小程序端点击tabbar的底层逻辑不触发uni.switchTab，需要在页面onShow生命周期中校验权限
onShow(async () => {
  console.log("tabbar page onShow");
  const hasPermission = await usePermission();
  console.log(hasPermission ? "已登录" : "未登录，拦截跳转");
  getMessageStatistics();
});

// 获取整体统计情况
const list = ref<IGetMessageStatisticsRes[]>();
const getMessageStatistics = async () => {
  try {
    showLoading();
    const data = await MessageApi.getMessageStatistics();
    list.value = data?.map((item) => {
      return {
        ...item,
        fun: () => {
          const handle = {
            todo_list: () => {
              uni.navigateTo({
                url: `/pages/tab/message/todoList?messageType=${item.type}`,
              });
            },
            notify_announcement: () => {
              uni.navigateTo({
                url: `/pages/tab/message/notice?messageType=${item.type}`,
              });
            },
            system_notify: () => {
              uni.navigateTo({
                url: `/pages/tab/message/systemMessage?messageType=${item.type}`,
              });
            },
          };
          handle[item.type] && handle[item.type]();
        },
      };
    });
    console.log(data);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const handleImg = ref({
  todo_list: "/static/images/message/ic_daiban.png",
  notify_announcement: "/static/images/message/ic_system.png",
  system_notify: "/static/images/message/ic_gonggao.png",
});
</script>
