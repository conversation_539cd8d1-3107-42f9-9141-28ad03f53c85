<template>
  <view pb-100rpx>
    <up-image
      :show-loading="true"
      src="/static/images/home/<USER>"
      width="100%"
      height="326rpx" />
    <view mt-(-226rpx) px-24rpx pb-32rpx>
      <up-swiper
        height="268rpx"
        radius="24rpx"
        :list="list"
        :autoplay="false"
        @change="onChange">
        <template #indicator>
          <view class="indicator">
            <view
              v-for="(item, index) in list"
              :key="index"
              class="indicator__dot"
              :class="[index === current && 'indicator__dot--active']" />
          </view>
        </template>
      </up-swiper>
      <ActivityControlPanel />
      <ToDoList :todo-title="todoTitle" />
    </view>
  </view>
</template>

<script setup lang="ts">
import type { TodoTitleRes } from "@/api/home/<USER>";
import { HomeApi } from "@/api";
import { useLoading } from "@/hooks";
import { useUserStore } from "@/store";
import { getToken, setToken, TokenKey } from "@/utils/auth";

const { showLoading, hideLoading } = useLoading();

const userStore = useUserStore();
const token = ref("");
onMounted(async () => {
  if (token.value) {
    setToken(token.value);
    await userStore.info();
    getBannerList();
    getTodoTitle();
  }
});

const list = ref<any>([]);

// 获取轮播图列表
const getBannerList = async () => {
  try {
    console.log("getBannerList===");
    showLoading();
    const data = await HomeApi.getBannerList();

    list.value = data?.map((item) => {
      return {
        url: JSON.parse(item.img_url)?.[0].url,
      };
    });
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const todoTitle = ref<TodoTitleRes>();
const getTodoTitle = async () => {
  try {
    console.log("todoTitle===");

    showLoading();
    const data = await HomeApi.todoTitle();
    console.log("todoTitle", data);
    todoTitle.value = data;
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const current = ref(0);
const onChange = (e) => {
  console.log("onChange", e);
  current.value = e.current;
};
// 获取query参数 user_code

onLoad((options) => {
  console.log("接收到的参数:", options);
  token.value = options?.token || getToken(TokenKey);
});
</script>

<style lang="scss" scoped>
.indicator {
  @include flex(row);

  justify-content: center;

  &__dot {
    width: 20rpx;
    height: 6rpx;
    margin: 0 10rpx;
    background-color: rgb(255 255 255 / 35%);
    border-radius: 30rpx;
    transition: background-color 0.3s;

    &--active {
      background-color: #fff;
    }
  }
}

.indicator-num {
  justify-content: center;
  width: 70rpx;
  padding: 4rpx 0;
  background-color: rgb(0 0 0 / 35%);
  border-radius: 200rpx;

  @include flex;

  &__text {
    font-size: 24rpx;
    color: #fff;
  }
}
</style>
