<template>
  <ContentBox title="我的" height="1186rpx" :background="bgImage">
    <view flex flex-col items-center>
      <view class="relative mt-72rpx">
        <Upload v-model="imgList" :action="action" :max-count="1">
          <image
            class="block h-240rpx w-240rpx rounded-full"
            :src="avatar"
            mode="scaleToFill" />
          <image
            class="absolute bottom-16rpx right-6rpx block h-54rpx w-54rpx"
            src="/static/images/ic_camera.png"
            mode="scaleToFill" />
        </Upload>
        <view
          v-if="imgList[0]?.status === 'uploading'"
          class="bg-color rounded-full"
          absolute
          bottom-0
          left-0
          right-0
          top-0
          h-full
          w-full
          center
          @tap="() => {}">
          <up-loading-icon mode="circle"></up-loading-icon>
        </view>
      </view>
      <view
        class="mb-24rpx mt-42rpx text-40rpx text-#292929 font-500 leading-[44rpx]">
        {{ userStore.name }}
      </view>
      <view class="text-28rpx text-#9E9E9E font-400 leading-[44rpx]">
        学号：{{ userStore.code }}
      </view>
    </view>

    <view mt-64rpx flex flex-col gap-y-24rpx p-24rpx>
      <view
        v-for="(item, index) in actionList"
        :key="index"
        class="between rounded-24rpx bg-white px-24rpx py-32rpx text-32rpx text-#212121 font-400 leading-[44rpx]"
        @click="item.fun">
        <view>{{ item.title }}</view>
        <view
          v-if="item.isMore"
          class="i-ep-arrow-right text-24rpx text-#A3A3A3"></view>
      </view>
    </view>
  </ContentBox>
</template>

<script setup lang="ts">
import type { UploadFile } from "@/components/common/type";
import { UserApi } from "@/api";
import { action, defaultAvatar } from "@/config";
import { useClipboard, useLoading, useModal, usePermission } from "@/hooks";
import ic_beiji from "@/static/images/ic_beiji.png";
import { useUserStore } from "@/store";
import { Toast, urlTobase64 } from "@/utils";

const bgImage = urlTobase64(ic_beiji);

const { showLoading, hideLoading } = useLoading();
const userStore = useUserStore();
// const { student_name } = storeToRefs(userStore);

const imgList = ref<UploadFile[]>([]);
const avatar = ref<string>(userStore.avatar || defaultAvatar);

const uerInfoEdit = async (params) => {
  await UserApi.uerInfoEdit(params);
  userStore.info();
};
watch(
  () => imgList.value,
  async (val) => {
    try {
      showLoading();
      if (val?.length) {
        if (val[0].status === "success") {
          await uerInfoEdit({
            avatar: val[0]?.url,
          });
          avatar.value = val[0]?.url;
          Toast("修改成功");
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  },
);

const { showModal } = useModal();
const actionList = ref([
  {
    title: "编辑资料",
    isMore: true,
    fun: () => {
      uni.navigateTo({
        url: "/pages/tab/user/edit",
      });
    },
  },
  {
    title: "退出登录",
    isMore: false,
    fun: async () => {
      try {
        const res: any = await showModal("确定退出登录吗？");
        if (res.confirm) {
          await userStore.logout();
          uni.$u.route({
            type: "reLaunch",
            url: "/pages/tab/home/<USER>",
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
  },
]);

const { setClipboardData, getClipboardData } = useClipboard();

// 复制
const toCopy = async () => {
  await setClipboardData({ data: "1234567890" });
  const data = await getClipboardData();
  console.log("[ data ] >", data);
};

// 登录鉴权，微信小程序端点击tabbar的底层逻辑不触发uni.switchTab，需要在页面onShow生命周期中校验权限
onShow(async () => {
  const hasPermission = await usePermission();
  console.log(hasPermission ? "已登录" : "未登录，拦截跳转");
});
</script>

<style lang="scss" scoped>
.bg-color {
  background: rgb(0 0 0 / 50%);
}
</style>
