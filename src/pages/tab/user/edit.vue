<template>
  <content-box title="个人资料">
    <view flex flex-col gap-y-24rpx p-24rpx>
      <view rounded-24rpx bg-white px-24rpx>
        <view v-for="(item, index) in actionList" :key="index">
          <view
            class="between rounded-24rpx bg-white py-18rpx text-30rpx text-#212121 font-400 leading-[44rpx]"
            @click="item.fun">
            <view h-72rpx leading-72rpx>{{ item.title }}</view>
            <view center>
              <Upload
                v-if="item.type === 'image'"
                v-model="userInfo[item.imgKey]"
                :action="action"
                :max-count="1">
                <image
                  class="block h-72rpx w-72rpx rounded-full"
                  :src="userInfo[item.key]"
                  mode="scaleToFill" />
              </Upload>

              <view v-else class="text-28rpx text-#9B9B9B">
                {{ userInfo[item.key] }}
              </view>
              <view
                class="i-ep-arrow-right ml-16rpx text-24rpx text-#A3A3A3"></view>
            </view>
          </view>
          <up-line color="#D8D8D8"></up-line>
        </view>
      </view>

      <view rounded-24rpx bg-white px-24rpx>
        <view v-for="(item, index) in studentInfoList" :key="index">
          <view
            class="between rounded-24rpx bg-white py-18rpx text-30rpx text-#212121 font-400 leading-[44rpx]">
            <view h-72rpx leading-72rpx>{{ item.title }}</view>
            <view class="text-28rpx text-#9B9B9B">
              {{ userInfo[item.key] }}
            </view>
          </view>
          <up-line color="#D8D8D8"></up-line>
        </view>
      </view>
    </view>

    <up-modal title="编辑昵称" :show="show" close-on-click-overlay>
      <template #default>
        <up-input
          v-model="name"
          placeholder="请输入内容"
          border="surround"
          font-size="30rpx"
          :custom-style="{
            background: '#F6F6F6',
            borderRadius: '12rpx',
            height: '96rpx',
          }"></up-input>
      </template>
      <template #confirmButton>
        <view flex justify-center gap-x-24rpx>
          <up-button
            :custom-style="{
              color: '#616161',
            }"
            text="取消"
            shape="circle"
            color="#F6F6F6"
            @click="onClose"></up-button>
          <up-button
            text="保存"
            shape="circle"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="onSave"></up-button>
        </view>
      </template>
    </up-modal>
    <!-- 时间选择 -->
    <up-datetime-picker
      v-model="dataValue"
      :show="showDate"
      confirm-color="#D63C38"
      mode="date"
      @cancel="onDateCancel"
      @confirm="onHandleDateConfirm" />

    <up-picker
      :show="showPicker"
      :columns="columns"
      confirm-color="#D63C38"
      key-name="label"
      @cancel="onCancel"
      @confirm="onHandleConfirm" />
  </content-box>
</template>

<script lang="ts" setup>
import type { IOption } from "@/utils/option";
import { UserApi } from "@/api";
import { enumApi } from "@/api/enums";
import { action, defaultAvatar } from "@/config";
import { useLoading } from "@/hooks";
import useDatePicker from "@/hooks/useDatePicker";
import usePicker from "@/hooks/usePicker";
import { useUserStore } from "@/store";
import { dateFormat, Toast } from "@/utils";

const { showLoading, hideLoading } = useLoading();
const userStore = useUserStore();

const userInfo = ref<any>({
  avatar: userStore.avatar || defaultAvatar,
  nickname: userStore.nickname,
  gender: userStore.gender,
  birthday: userStore.birthday,
  code: userStore.code,
  department_name: userStore.department_name,
  phone_number: userStore.phone_number,
  fileList: [],
});

const uerInfoEdit = async (params) => {
  await UserApi.uerInfoEdit(params);
  userStore.info();
};

watch(
  () => userInfo.value.fileList,
  async (val) => {
    try {
      showLoading();
      if (val.length) {
        if (val[0].status === "success") {
          await uerInfoEdit({
            avatar: val[0]?.url,
          });
          userInfo.value.avatar = val[0]?.url;
          Toast("修改成功");
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  },
);

const actionList = ref([
  {
    title: "头像",
    type: "image",
    imgKey: "fileList",
    key: "avatar",
  },
  {
    title: "昵称",
    key: "nickname",
    fun: () => {
      show.value = true;
    },
  },
  {
    title: "性别",
    key: "gender",
    fun: () => {
      showModal("gender");
    },
  },
  {
    title: "出生日期",
    key: "birthday",
    fun: () => {
      onShowDate("birthday");
    },
  },
]);

const studentInfoList = ref([
  {
    title: "学号",
    key: "code",
  },
  {
    title: "学校/企业",
    key: "department_name",
  },
  {
    title: "手机号",
    key: "phone_number",
  },
]);

const show = ref<boolean>(false);
const name = ref<string>("");
const onClose = () => {
  show.value = false;
};
const onSave = async () => {
  try {
    showLoading();
    await uerInfoEdit({
      nickname: name.value,
    });
    Toast("修改成功");
    userInfo.value.nickname = name.value;
    show.value = false;
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const onHandleDateConfirm = async (e?) => {
  try {
    showLoading();
    await uerInfoEdit({
      birthday: dateFormat(e.value, e.mode),
    });
    Toast("修改成功");
    onDateConfirm(e);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const onHandleConfirm = async (e) => {
  try {
    showLoading();
    await uerInfoEdit({
      gender: e.value[0]?.label,
    });
    Toast("修改成功");
    onConfirm(e);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

// 时间选择
const { dataValue, showDate, onShowDate, onDateCancel, onDateConfirm } =
  useDatePicker(userInfo);

const { showPicker, showModal, onCancel, onConfirm } = usePicker(userInfo);
const columns = ref<IOption[][]>([enumApi.get("GENDER").options]);
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;

  .u-button__text {
    font-size: 32rpx !important;
  }
}

::v-deep .u-modal {
  border-radius: 24rpx;

  .u-modal__content {
    padding: 60rpx 48rpx !important;
  }

  .u-modal__title {
    font-size: 34rpx;
    line-height: 44rpx;
    color: #212121;
  }
}
</style>
