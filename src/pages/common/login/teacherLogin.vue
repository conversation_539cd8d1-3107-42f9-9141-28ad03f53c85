<template>
  <view class="">
    <view relative>
      <image
        class="block h-636rpx w-full"
        src="/static/images/ic_bglogin.png"
        mode="scaleToFill" />
      <view
        absolute
        left-40rpx
        top-72rpx
        text-44rpx
        text-white
        font-500
        leading-62rpx>
        <view mb-16rpx>HELLO，</view>
        <view>欢迎登录实习实训平台</view>
      </view>
    </view>
    <view relative mt--356rpx p-24rpx>
      <view class="rounded-24rpx bg-white p-24rpx px-40rpx py-80rpx">
        <up-form
          ref="formRef"
          label-position="left"
          :model="userInfo"
          :rules="rules">
          <up-form-item prop="phone_number">
            <up-input
              v-model="userInfo.phone_number"
              border="none"
              font-size="30rpx"
              placeholder="请输入手机号">
              <template #prefix>
                <image
                  class="mr-30rpx block h-40rpx w-40rpx"
                  src="/static/images/ic_phone.png"
                  mode="scaleToFill" />
              </template>
            </up-input>
          </up-form-item>
        </up-form>
        <up-button
          text="登录"
          shape="circle"
          color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
          @click="onSubmit" />
        <view mt-36rpx center>
          <up-checkbox active-color="red">
            <template #label>
              <view center text-24rpx leading-28rpx>
                <view class="text-#9B9B9B">我已阅读并同意</view>
                <view class="text-#212121">《用户协议》和《隐私政策》</view>
              </view>
            </template>
          </up-checkbox>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useCompanyUserStore } from "@/store";

// 使用 ref 创建响应式引用
const formRef = ref();
const userInfo = ref({
  phone_number: "15200000000",
});
const rules = ref({
  phone_number: {
    type: "string",
    required: true,
    message: "请输入手机号",
    trigger: ["blur", "change"],
  },
});

const tips = ref("");
const uCodeRef = ref();
const codeChange = (text) => {
  tips.value = text;
};
const getCode = () => {
  if (uCodeRef.value.canGetCode) {
    // 模拟向后端请求验证码
    uni.showLoading({
      title: "正在获取验证码",
    });
    setTimeout(() => {
      uni.hideLoading();
      // 这里此提示会被start()方法中的提示覆盖
      uni.$u.toast("验证码已发送");
      // 通知验证码组件内部开始倒计时
      uCodeRef.value.start();
    }, 2000);
  } else {
    uni.$u.toast("倒计时结束后再发送");
  }
};

const internshipPlanId = ref<number>();
const studentId = ref<number>();
onLoad((options) => {
  internshipPlanId.value = Number(options?.internshipPlanId || 1);
  studentId.value = options?.studentId || "144563";
});

const useCompanyUser = useCompanyUserStore();
const onSubmit = async () => {
  try {
    await formRef.value.validate();

    await useCompanyUser.login({
      ...userInfo.value,
      internship_plan_id: internshipPlanId.value,
      student_id: studentId.value,
    });

    // uni.$u.route({
    //   type: isTabBarPath(redirect) ? "switchTab" : "redirectTo",
    //   url: redirect,
    // });
    uni.navigateTo({
      url: "/pages/active/myGrades/corporateRating",
    });
  } catch (error) {
    console.log(error);
  }
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 70rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}

::v-deep .u-input {
  height: 96rpx;
  padding: 28rpx 32rpx !important;
  background: #f8f8f8;
  border-radius: 24rpx !important;
}

// ::v-deep .u-text {
//   position: relative;
//   &::before {
//     position: absolute;
//     left: -32rpx;
//     top: 0;
//     content: none;
//     width: 10px;
//     height: 100%;
//     background: #eaeaea;
//   }
// }
</style>
