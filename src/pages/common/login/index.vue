<template>
  <view class="h-full bg-white">
    <view relative>
      <image class="block h-1164rpx w-full" :src="loginBg" mode="scaleToFill" />
      <view
        absolute
        left-0rpx
        right-0rpx
        top-242rpx
        center
        flex-col
        text-44rpx
        text-white
        font-500
        leading-62rpx>
        <image
          class="block h-192rpx w-192rpx"
          :src="loginLogo"
          mode="scaleToFill" />
        <view class="mt-32rpx text-36rpx text-#212121 font-700 leading-44rpx">
          实习实训学生端
        </view>
      </view>
    </view>
    <view relative mt--600rpx p-24rpx>
      <view class="rounded-24rpx p-24rpx px-40rpx">
        <up-form
          ref="formRef"
          label-position="left"
          :model="userInfo"
          :rules="rules">
          <up-form-item prop="user_code">
            <up-input
              v-model="userInfo.user_code"
              border="none"
              font-size="30rpx"
              placeholder="请输入账号">
              <template #prefix>
                <image
                  class="mr-30rpx block h-40rpx w-40rpx"
                  src="/static/images/ic_phone.png"
                  mode="scaleToFill" />
              </template>
            </up-input>
          </up-form-item>
          <up-form-item prop="password">
            <up-input
              v-model="userInfo.password"
              border="none"
              font-size="30rpx"
              placeholder="请输入密码"
              type="password">
              <template #prefix>
                <image
                  class="mr-30rpx block h-40rpx w-40rpx"
                  src="/static/images/ic_yzm.png"
                  mode="scaleToFill" />
              </template>
              <!-- <template #suffix>
                <up-code
                  ref="uCodeRef"
                  seconds="30"
                  change-text="X秒重新获取"
                  @change="codeChange"></up-code>
                <up-text
                  size="30rpx"
                  color="#D63C38"
                  :text="tips"
                  @tap="getCode"></up-text>
              </template> -->
            </up-input>
          </up-form-item>
        </up-form>
        <up-button
          text="登录"
          shape="circle"
          color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
          @click="onSubmit" />
        <view mt-36rpx center>
          <up-checkbox active-color="red">
            <template #label>
              <view center text-24rpx leading-28rpx>
                <view class="text-#9B9B9B">我已阅读并同意</view>
                <view class="text-#212121">《用户协议》和《隐私政策》</view>
              </view>
            </template>
          </up-checkbox>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  HOME_PATH,
  isTabBarPath,
  LOGIN_PATH,
  removeQueryString,
} from "@/router";
import { useUserStore } from "@/store";

const loginBg =
  "https://chinadxscy.csu.edu.cn/files/ai-employee/2025/05/19/12c52f2c28194f7f96a3ac0227ceba63.png";
const loginLogo =
  "https://chinadxscy.csu.edu.cn/files/ai-employee/2025/05/19/8f7742bd8f4e47cc8218fc74cf0cf1e1.png";

let redirect = HOME_PATH;
// 使用 ref 创建响应式引用
const formRef = ref();
const userInfo = ref({
  user_code: "0101230101",
  password: "123456",
});
const rules = ref({
  user_code: {
    type: "string",
    required: true,
    message: "请输入账号",
    trigger: ["blur", "change"],
  },
  password: {
    type: "string",
    required: true,
    message: "请输入密码",
    trigger: ["blur", "change"],
  },
});

const tips = ref("");
const uCodeRef = ref();
const codeChange = (text) => {
  tips.value = text;
};
const getCode = () => {
  if (uCodeRef.value.canGetCode) {
    // 模拟向后端请求验证码
    uni.showLoading({
      title: "正在获取验证码",
    });
    setTimeout(() => {
      uni.hideLoading();
      // 这里此提示会被start()方法中的提示覆盖
      uni.$u.toast("验证码已发送");
      // 通知验证码组件内部开始倒计时
      uCodeRef.value.start();
    }, 2000);
  } else {
    uni.$u.toast("倒计时结束后再发送");
  }
};

const userStore = useUserStore();
const onSubmit = async () => {
  try {
    await formRef.value.validate();
    // console.log("Form validation result:", valid);

    await userStore.login(userInfo.value);
    uni.$u.route({
      type: isTabBarPath(redirect) ? "switchTab" : "redirectTo",
      url: redirect,
    });
  } catch (error) {
    console.log(error);
  }
};

onLoad((options: any) => {
  if (options.redirect && removeQueryString(options.redirect) !== LOGIN_PATH) {
    redirect = decodeURIComponent(options.redirect);
  }
});
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 70rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}

::v-deep .u-input {
  height: 96rpx;
  padding: 28rpx 32rpx !important;
  background: #f8f8f8;
  border-radius: 24rpx !important;
}

// ::v-deep .u-text {
//   position: relative;
//   &::before {
//     position: absolute;
//     left: -32rpx;
//     top: 0;
//     content: none;
//     width: 10px;
//     height: 100%;
//     background: #eaeaea;
//   }
// }
</style>
