<template>
  <view class="questionnaire-container">
    <!-- 问题内容区域 -->
    <view class="question-content mb-30rpx">
      <view class="between">
        <view class="question-type">{{ title }}</view>
        <view>
          <view class="question-progress">
            <text class="current">{{ currentQuestion + 1 }}</text>
            <text class="total">/{{ questionInfo.question_number }}</text>
          </view>
        </view>
      </view>

      <view class="question-text">
        {{
          questionData.question_type !== QuestionTypeEnum.Subjective
            ? `（${selectedOption} ）`
            : ""
        }}
        {{ questionData.question_content }}
      </view>

      <!-- 主观题 -->
      <view v-if="questionData.question_type === QuestionTypeEnum.Subjective">
        <up-line color="#D8D8D8" dashed></up-line>
        <up-textarea
          v-model="selectedOption"
          border="none"
          height="416rpx"
          :maxlength="500"
          placeholder="请输入答案"
          count></up-textarea>
      </view>
      <!-- 选项列表 -->
      <view v-else class="options-list">
        <view
          v-for="option in questionData.options"
          :key="option.value"
          class="option-item"
          :class="{ selected: selectedOption === option.value }"
          @click="selectOption(option.value)">
          <!-- <text class="option-label">{{ option.value }}</text> -->
          <text class="option-text">{{ option.label }}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <up-button
      :text="isFinish ? '提交' : '下一题'"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="goToNextQuestion" />

    <QuestionTip v-model="showFinish" @back="onBack" @finish="onBack" />
  </view>
</template>

<script lang="ts" setup>
import type {
  GetSurveyQuestionRes,
  Question,
  SubmitSurveyReq,
} from "@/api/surveyPaper/types";
import { SurveyPaperApi } from "@/api";
import { QuestionTypeEnum } from "@/api/enums/value";
import { useLoading } from "@/hooks";
import { useApplicationStore } from "@/store";

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const serveyPaperId = ref<number>();
onLoad((options) => {
  console.log("接收到的参数:", options);
  serveyPaperId.value = Number(options?.serveyPaperId);
});

// 页面加载时获取问卷列表
onMounted(() => {
  getSurveyQuestion();
});

// 获取问卷所有题目
const questionInfo = ref<GetSurveyQuestionRes>({
  question_number: 0,
});
// 模拟问题数据
const questionList = ref<Question[]>();
const getSurveyQuestion = async () => {
  try {
    showLoading();
    const data = await SurveyPaperApi.getSurveyQuestion({
      serveyPaperId: serveyPaperId.value,
    });
    questionInfo.value = data;
    questionList.value = data.questions?.map((item) => {
      return {
        ...item,
        options: JSON.parse(item.options || ""),
      };
    });

    console.log("getSurveyQuestion", data);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const title = computed(() => {
  const handle = {
    "Single-Choice": "单项选择题",
    Judgment: "判断题",
    Subjective: "主观题",
  };
  return (
    questionData.value?.question_type &&
    handle?.[questionData.value.question_type]
  );
});

// 是否是最后一题
const isFinish = computed(() => {
  return currentQuestion.value + 1 >= questionInfo.value?.question_number;
});

const showFinish = ref<boolean>(false);

// 当前问题索引
const currentQuestion = ref(0);
const selectedOption = ref("");
const answerList = ref<SubmitSurveyReq[]>([]);
const questionData = computed<any>(() => {
  return questionList.value?.[currentQuestion.value] || {};
});

// 选择选项
const selectOption = (value: string) => {
  selectedOption.value = value;
};

// 跳转到下一题
const goToNextQuestion = async () => {
  if (!selectedOption.value) {
    uni.showToast({
      title: "请填写答案",
      icon: "none",
    });
    return;
  }

  // 保存答案
  if (answerList.value.length < questionInfo.value.question_number) {
    answerList.value.push({
      question_id: questionData.value.id,
      student_answer: selectedOption.value,
    });
  }

  // 如果是最后一题，提交答案
  if (isFinish.value) {
    try {
      showLoading();
      const data = await SurveyPaperApi.submitSurvey(answerList.value, {
        internshipPlanId: currentPlanInfo.value.id,
        serveyPaperId: serveyPaperId.value,
      });
      console.log("submitSurvey", data);

      showFinish.value = true;
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
    return;
  }

  // 跳转到下一题
  currentQuestion.value += 1;
  selectedOption.value = "";
};

const onBack = () => {
  uni.redirectTo({
    url: "/pages/active/questionnaire/index",
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 48rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}

.questionnaire-container {
  padding: 24rpx;
  background-color: #f7f7f7;
}

.question-content {
  padding: 38rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;

  .question-type {
    margin-bottom: 32rpx;
    font-size: 32rpx;
    line-height: 44rpx;
    color: #d63c38;
  }

  .question-progress {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30rpx;

    .current {
      font-size: 40rpx;
      font-weight: 500;
      line-height: 40rpx;
      color: #d63c38;
    }

    .total {
      font-size: 28rpx;
      line-height: 50rpx;
      color: #9e9e9e;
    }
  }

  .question-text {
    margin-bottom: 48rpx;
    font-size: 34rpx;
    line-height: 64rpx;
    color: #141519;
  }
}

.options-list {
  color: #141519;

  .option-item {
    display: flex;
    align-items: flex-start;
    padding: 34rpx 32rpx;
    margin-bottom: 20rpx;
    color: #141519;
    background-color: #fafbfc;
    border-radius: 16rpx;

    &.selected {
      color: #d63c38;
      background: linear-gradient(
        272deg,
        rgb(214 60 56 / 15%) 3%,
        rgb(231 107 103 / 15%) 100%
      );
    }

    .option-label {
      margin-right: 36rpx;
      font-size: 34rpx;
    }

    .option-text {
      flex: 1;
      font-size: 34rpx;
    }
  }
}
</style>
