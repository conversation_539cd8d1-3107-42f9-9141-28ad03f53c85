<template>
  <ContentBox
    title="实习申请"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <view p-20rpx>
      <!-- 空列表 -->
      <view
        v-if="!list?.length"
        class="rounded-24rpx bg-white px-40rpx pb-96rpx pt-122rpx">
        <up-empty
          width="600rpx"
          height="100%"
          icon="/static/images/tip/img_kong.png"
          text="暂无实习岗位">
          <view class="mt-50rpx w-630rpx flex flex-col gap-y-32rpx">
            <up-button
              text="实习申请"
              shape="circle"
              :disabled="disabledApply"
              color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
              @click="goApplication(ModeEnum.ADD)" />
            <up-button
              :custom-style="{
                border: '1px solid #D63C38',
                color: '#D63C38',
              }"
              :disabled="disabledApply"
              text="免实习申请"
              color="#FFE7E6"
              shape="circle"
              @click="goFreeApplication(ModeEnum.ADD)" />
          </view>
        </up-empty>
      </view>
      <!-- list -->
      <view v-else>
        <CardItem
          v-for="(item, index) in list"
          :key="index"
          :item="item"
          :init-data="initData"
          :title="`${userStore.name}的实习申请`">
          <view
            v-if="item.audit_status === StatusEnum.approved"
            flex
            gap-x-16rpx>
            <up-button
              :custom-style="customStyle"
              color="#FFE7E6"
              text="实习进度"
              shape="circle"
              @click="goProgress"></up-button>
            <up-button
              :custom-style="customStyle"
              color="#FFE7E6"
              text="实习变更"
              shape="circle"
              @click="goChangeApplication"></up-button>
            <up-button
              :custom-style="customStyle"
              color="#FFE7E6"
              text="结束实习"
              shape="circle"
              @click="onFinish"></up-button>
          </view>
          <up-button
            v-else-if="item.audit_status === StatusEnum.pending"
            :custom-style="{
              border: '1px solid #9B9B9B',
              color: '#9B9B9B',
              width: '155rpx',
              height: '56rpx',
            }"
            text="撤销申请"
            shape="circle"
            @click="revoke(item)"></up-button>

          <up-button
            v-else-if="
              item.audit_status === StatusEnum.rejected ||
              item.audit_status === StatusEnum.revoked
            "
            :custom-style="customStyle"
            color="#FFE7E6"
            text="重新提交"
            shape="circle"
            @click="goApplication(ModeEnum.EDIT, item)"></up-button>

          <up-button
            :custom-style="{
              width: '155rpx',
              height: '56rpx',
            }"
            text="实习详情"
            shape="circle"
            color="linear-gradient(272deg, #D63C38 3%, #E76B67 100%)"
            @click="goDetail(item)"></up-button>
        </CardItem>
      </view>
    </view>
    <FinishModal v-model="isShowModal" @confirm="onConfirm" />
  </ContentBox>
</template>

<script lang="ts" setup>
import type { StudentApplicationList } from "@/api/internshipProgram/types";
import { InternshipProgramApi } from "@/api";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import { useUserStore } from "@/store";
import useApplicationStore from "@/store/modules/application";
import { setToken, Toast, urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const userStore = useUserStore();
const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();
const token = ref("");
onMounted(async () => {
  if (token.value) {
    setToken(token.value);
    // await userStore.info();
  }
});
onLoad((options) => {
  console.log("接收到的参数:", options);
  token.value = options?.token;
});
const bgImage = urlTobase64(smallNavBarBg);

const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});

const list = ref<StudentApplicationList[]>();

const initData = ref([
  {
    label: "单位名称",
    key: "company_name",
  },
  {
    label: "岗位",
    key: "position_name",
  },
  {
    label: "实习时间",
    key: "expected_start_date",
  },
]);

// 检查学生是否可以申请实习计划
const disabledApply = ref<boolean>(true);

// 获取实习申请列表
const getApplicationList = async () => {
  const data = await InternshipProgramApi.studentApplicationList({
    internshipPlanId: currentPlanInfo.value.id,
  });
  list.value = data.applications;
  disabledApply.value = !data.can_apply;
  console.log("getApplicationList", data);
};

const init = debounce(async () => {
  try {
    showLoading();
    await getApplicationList();
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
}, 300);

watch(
  () => currentPlanInfo.value.id,
  (val) => {
    val && init();
  },
);

onShow(() => {
  currentPlanInfo.value?.id && init();
});

// 撤销实习计划申请
const revokeApplication = async (item) => {
  const res = await InternshipProgramApi.revokeApplication({
    internshipPlanId: currentPlanInfo.value.id,
    applicationId: item.id,
  });
  console.log(res);
};

// 实习结束弹框
const isShowModal = ref<boolean>(false);
const onFinish = () => {
  isShowModal.value = true;
};
const onConfirm = async () => {
  showLoading();
  await createEndInternshipApplication();
  await getApplicationList();
  hideLoading();
};
// 创建学生的实习计划结束申请
const createEndInternshipApplication = async () => {
  const res = await InternshipProgramApi.createEndInternshipApplication({
    internshipPlanId: currentPlanInfo.value.id,
  });
  console.log(res);
};

const goProgress = () => {
  uni.navigateTo({
    url: "/pages/active/internshipApplication/progress",
  });
};

// 撤销申请
const { showModal } = useModal();
const revoke = async (item) => {
  try {
    showLoading();
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      await revokeApplication(item);
      Toast("撤销成功");
      getApplicationList();
    }
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const goChangeApplication = () => {
  uni.navigateTo({
    url: `/pages/active/internshipChange/application?mode=${ModeEnum.ADD}`,
  });
};

const goApplication = (type, item?) => {
  console.log("goDetail", item);
  if (item) {
    uni.navigateTo({
      url: `/pages/active/internshipApplication/application?mode=${type}&applicationId=${item?.id}`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/active/internshipApplication/application?mode=${type}`,
    });
  }
};

const goFreeApplication = (type, item?) => {
  console.log("goDetail", item);
  if (item) {
    uni.navigateTo({
      url: `/pages/active/avoidInternship/application?mode=${type}&applicationId=${item?.id}`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/active/avoidInternship/application?mode=${type}`,
    });
  }
};

const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/internshipApplication/detail?applicationId=${item.id}`,
  });
};
</script>

<style lang="scss" scoped></style>
