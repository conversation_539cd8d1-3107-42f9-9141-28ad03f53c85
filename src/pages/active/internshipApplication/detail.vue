<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <DetailCard
      :item="detail"
      :init-data="initData"
      :title="`${userStore.name}的实习申请`">
      <template #match>
        <Match :position-id="detail.position_id" />
      </template>
      <template #time>
        <view between py-20rpx>
          <view flex items-center>
            <view
              class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
            <view>上班时间</view>
          </view>
          <view>{{ detail.work_time }}</view>
        </view>
        <up-line color="#f1f1f1" dashed></up-line>
        <view between pt-20rpx>
          <view flex items-center>
            <view
              class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
            <view>下班时间</view>
          </view>
          <view>{{ detail.off_time }}</view>
        </view>
      </template>
    </DetailCard>

    <AttachmentDetail :form-data="formData" />

    <Process :list="stepsList" />

    <!-- 审批中展示 -->
    <view
      v-if="detail.audit_status === StatusEnum.pending"
      class="mt-50rpx flex flex-col gap-y-32rpx">
      <up-button
        text="撤销"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="onRevoke" />
    </view>
    <!-- 驳回展示 -->
    <up-button
      v-if="
        detail.audit_status === StatusEnum.rejected ||
        detail.audit_status === StatusEnum.revoked
      "
      text="重新提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onResubmit" />
  </view>
</template>

<script lang="ts" setup>
import type { GetPlanApprovalFlowRes } from "@/api/internshipProgram/types";
import type { IAttachmentInfoForm } from "@/components/internshipApplication/type";
import { InternshipProgramApi } from "@/api";
import { enumApi } from "@/api/enums";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { useUserStore } from "@/store";
import useApplicationStore from "@/store/modules/application";
import { Toast } from "@/utils";

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const applicationId = ref<number>(0);
onLoad((options) => {
  applicationId.value = Number(options?.applicationId);
  console.log("onLoad===", options);
});

onMounted(async () => {
  try {
    showLoading();
    await Promise.all([getApplicationDetail(), getPlanApprovalFlow()]);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
});

// 获取实习申请详情
const getApplicationDetail = async () => {
  try {
    const data = await InternshipProgramApi.getApplicationDetail({
      applicationId: applicationId.value,
    });
    console.log("getApplicationDetail", data);
    detail.value = {
      ...data.company,
      ...data.position,
      ...data.application,
    };
    detail.value.matchLabel = enumApi
      .get("YN")
      .findLabel(detail.value.is_match);
    formData.value.agreement_attachment = JSON.parse(
      data.application?.agreement_attachment,
    );
    formData.value.insurance_attachment = JSON.parse(
      data.application?.insurance_attachment,
    );
    formData.value.other_attachment = JSON.parse(
      data.application?.other_attachment,
    );

    console.log("formData.value", formData.value);
  } catch (err) {
    console.log(err);
  }
};

// 撤销实习计划申请
const revokeApplication = async () => {
  const res = await InternshipProgramApi.revokeApplication({
    internshipPlanId: currentPlanInfo.value.id,
    applicationId: 1,
  });
  console.log(res);
};

// 获取实习计划申请批流程
const getPlanApprovalFlow = async () => {
  const data = await InternshipProgramApi.getPlanApprovalFlow({
    applicationId: applicationId.value,
  });
  stepsList.value = data;
  console.log("getPlanApprovalFlow", data);
};

// 详情页
const detail = ref<any>({});
const formData = ref<IAttachmentInfoForm>({});
const initData = ref([
  {
    label: "审核编号",
    key: "code",
  },
  {
    label: "提交时间",
    key: "created_at",
  },
  {
    label: "企业信息",
  },
  {
    label: "实习单位",
    key: "company_name",
  },
  {
    label: "企业性质",
    key: "nature",
  },
  {
    label: "行业内别",
    key: "industry_category",
  },
  {
    label: "统一社会信用代码",
    key: "credit_code",
  },
  {
    label: "企业地址",
    key: "address",
  },
  {
    label: "岗位信息",
  },
  {
    label: "匹配度",
    key: "time",
    type: "slot",
    slotName: "match",
  },
  {
    label: "岗位名称",
    key: "position_name",
  },
  {
    label: "岗位类别",
    key: "industry",
  },
  {
    label: "岗位介绍",
    key: "description",
  },
  {
    label: "工作内容",
    key: "internship_content",
  },
  {
    label: "工作地点",
    key: "location",
  },
  {
    label: "打卡时间",
    key: "time",
    type: "slot",
    slotName: "time",
  },
  {
    label: "企业老师",
    key: "company_teacher_name",
  },
  {
    label: "企业老师电话",
    key: "company_teacher_phone_number",
  },
  {
    label: "实习信息",
  },
  {
    label: "开始时间",
    key: "expected_start_date",
  },
  {
    label: "结束时间",
    key: "expected_end_date",
  },
  {
    label: "专业是否对口",
    key: "matchLabel",
  },
  {
    label: "实习薪酬",
    key: "position_salary",
  },
]);

// 审批流程
const stepsList = ref<GetPlanApprovalFlowRes[]>([]);

const { showModal } = useModal();
const onRevoke = async () => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await revokeApplication();
      Toast("撤销成功");
      uni.navigateBack();
    }
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
const onResubmit = async () => {
  uni.redirectTo({
    url: `/pages/active/internshipApplication/application?mode=${ModeEnum.EDIT}&applicationId=${applicationId.value}`,
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
