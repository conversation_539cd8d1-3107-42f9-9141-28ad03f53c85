<template>
  <view class="p-24rpx">
    <InternshipProgress
      v-for="(item, index) in progressData"
      :key="index"
      :item="item" />
  </view>
</template>

<script lang="ts" setup>
import type { IProgressData } from "@/components/internshipApplication/type";
import { InternshipProgramApi } from "@/api";
import { useLoading } from "@/hooks";
import { useApplicationStore } from "@/store";

const progressData = ref<IProgressData[]>();

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();
onMounted(() => {
  getStudentProgress();
});

const getStudentProgress = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getStudentProgress({
      internshipPlanId: currentPlanInfo.value.id,
    });

    progressData.value = [
      {
        title: "日报",
        sum: data.total_daily_report_number,
        seriesData: [
          {
            name: "已完成",
            value: data.daily_report_number,
            color: "#E1665F",
          },
          {
            name: "未完成",
            value: data.un_daily_report_number,
            color: "#507AFC",
          },
        ],
      },
      {
        title: "周报",
        sum: data.total_weekly_report_number,
        seriesData: [
          {
            name: "已完成",
            value: data.weekly_report_number,
            color: "#E1665F",
          },
          {
            name: "未完成",
            value: data.un_weekly_report_number,
            color: "#FAC858",
          },
        ],
      },
      {
        title: "月报",
        sum: data.total_monthly_report_number,
        seriesData: [
          {
            name: "已完成",
            value: data.monthly_report_number,
            color: "#1FADE5",
          },
          {
            name: "未完成",
            value: data.un_monthly_report_number,
            color: "#887DFF",
          },
        ],
      },
      {
        title: "总结",
        sum: data.total_summary_number,
        seriesData: [
          {
            name: "已完成",
            value: data.summary_number,
            color: "#507AFC",
          },
          {
            name: "未完成",
            value: data.un_summary_number,
            color: "#FAC858",
          },
        ],
      },
      {
        title: "考勤",
        sum: data.total_attendance_number,
        seriesData: [
          {
            name: "已完成",
            value: data.attendance_number,
            color: "#E1665F",
          },
          {
            name: "未完成",
            value: data.un_attendance_number,
            color: "#EFBCB9",
          },
        ],
      },
    ];
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
</script>

<style lang="scss" scoped>
//
</style>
