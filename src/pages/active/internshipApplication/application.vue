<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <Information
      :id="currentPlanInfo.id"
      ref="informationRef"
      v-model="informationData"
      :is-self="true"
      @change="onChange" />
    <JobInfo
      :id="currentPlanInfo.id"
      ref="jobInfoRef"
      v-model="jobInfoData"
      :company-id="companyId" />
    <InternshipInfo ref="internshipInfoRef" v-model="internshipInfoData" />
    <AttachmentInfo
      ref="attachmentInfoRef"
      v-model:form-info="attachmentInfoData" />
    <up-button
      class="custom-style"
      :custom-style="{
        height: '88rpx',
        marginTop: '24rpx',
        fontSize: '32rpx',
        fontWeight: '500',
      }"
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      :disabled="disabledApply"
      @click="onSubmit" />
    <Tip v-model="showTip" />
  </view>
</template>

<script lang="ts" setup>
import type { StartStudentApplicationRep } from "@/api/internshipProgram/types";
import type {
  IAttachmentInfoForm,
  IGetCompanyDetailRes,
  IGetPositionDetailRes,
} from "@/components/internshipApplication/type";
import type { IOption } from "@/utils/option";
import { InternshipProgramApi } from "@/api";
import { enumApi } from "@/api/enums";
import { ModeEnum } from "@/api/enums/value";
import AttachmentInfo from "@/components/internshipApplication/AttachmentInfo.vue";
import Information from "@/components/internshipApplication/Information.vue";
import InternshipInfo from "@/components/internshipApplication/InternshipInfo.vue";
import JobInfo from "@/components/internshipApplication/JobInfo.vue";
import { useLoading } from "@/hooks";
import useApplicationStore from "@/store/modules/application";
import { Toast } from "@/utils";

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const applicationId = ref<number>(0);
const mode = ref<ModeEnum>(ModeEnum.ADD);
onLoad((options) => {
  applicationId.value = Number(options?.applicationId);
  mode.value = options?.mode;
  console.log("onLoad===", options);
});

const companyId = ref<number>(0);
const onChange = (val: IOption) => {
  console.log("onChange===", val);
  companyId.value = val.value;
};

onMounted(async () => {
  showLoading();
  checkCanApply();
  isEdit.value && (await getApplicationDetail());
  hideLoading();
});

// 检查学生是否可以申请实习计划
const disabledApply = ref<boolean>(true);
const checkCanApply = async () => {
  try {
    await InternshipProgramApi.checkCanApply({
      internshipPlanId: currentPlanInfo.value.id,
    });
    disabledApply.value = false;
  } catch (err) {
    console.log(err);
    disabledApply.value = true;
  }
};

// 获取实习申请详情
const getApplicationDetail = async () => {
  try {
    const data = await InternshipProgramApi.getApplicationDetail({
      applicationId: applicationId.value,
    });
    // detailInfo.value = data;
    const detailInfo: any = {
      ...data.application,
      ...data.company,
      ...data.position,
    };

    detailInfo.matchLabel = enumApi.get("YN").findLabel(detailInfo.is_match);

    console.log("data", detailInfo);

    informationData.value = detailInfo;
    jobInfoData.value = detailInfo;
    internshipInfoData.value = detailInfo;
    attachmentInfoData.value = data.application as any;

    console.log("attachmentInfoData.value", attachmentInfoData.value);
  } catch (err) {
    console.log(err);
  }
};

const showTip = ref<boolean>(false);

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const informationRef = ref<InstanceType<typeof Information>>();
const jobInfoRef = ref<InstanceType<typeof JobInfo>>();
const internshipInfoRef = ref<InstanceType<typeof InternshipInfo>>();
const attachmentInfoRef = ref<InstanceType<typeof AttachmentInfo>>();
// form数据
const informationData = ref<IGetCompanyDetailRes>({
  company_name: "",
  company_id: undefined,
  nature: "",
  industry_category: "",
  credit_code: "",
  agreement_end_time: "",
  principal: "",
  phone_number: "",
  address: "",
});
const jobInfoData = ref<IGetPositionDetailRes>({
  position_name: "",
  position_id: undefined,
  major_type: "",
  description: "",
  internship_content: "",
  location: "",
  work_time: "",
  off_time: "",
  company_teacher_name: "",
  company_teacher_phone_number: "",
});
const internshipInfoData = ref<StartStudentApplicationRep>({
  expected_start_date: "",
  expected_end_date: "",
  is_match: undefined,
  matchLabel: "",
  position_salary: undefined,
});
const attachmentInfoData = ref<IAttachmentInfoForm>({
  agreement_attachment: [],
  insurance_attachment: [],
  other_attachment: [],
});
const onSubmit = () => {
  Promise.all([
    informationRef.value?.formRef.validate(),
    jobInfoRef.value?.formRef.validate(),
    internshipInfoRef.value?.formRef.validate(),
    attachmentInfoRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      const attachments = [
        {
          type: "agreement_attachment",
          items: attachmentInfoData.value.agreement_attachment,
        },
        {
          type: "insurance_attachment",
          items: attachmentInfoData.value.insurance_attachment,
        },
        {
          type: "other_attachment",
          items: attachmentInfoData.value.other_attachment,
        },
      ];
      const fileList = {};
      attachments.forEach((item) => {
        fileList[item.type] = JSON.stringify(item.items);
      });
      const allInfo: any = {
        internship_plan_id: currentPlanInfo.value.id,
        ...informationData.value,
        ...jobInfoData.value,
        ...internshipInfoData.value,
        ...fileList,
      };
      console.log("All forms validated successfully", allInfo);
      try {
        // 提交表单数据
        let res;
        if (isAdd.value) {
          res = await InternshipProgramApi.startStudentApplication(allInfo, {
            internshipPlanId: currentPlanInfo.value.id,
          });
          Toast("申请成功");
        } else {
          res = await InternshipProgramApi.editStudentApplication(allInfo, {
            internshipPlanId: currentPlanInfo.value.id,
            applicationId: applicationId.value,
          });
          Toast("修改成功");
        }
        uni.navigateBack();

        console.log("Form submitted successfully", res);
      } catch (error) {
        console.error("Error submitting form", error);
      }
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    });
};
</script>

<style lang="scss" scoped>
.custom-style {
  ::v-deep(.u-button) {
    text {
      font-size: 32rpx;
    }
  }
}
</style>
