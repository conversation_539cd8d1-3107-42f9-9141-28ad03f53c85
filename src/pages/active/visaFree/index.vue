<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <VisaFreeApply
      v-if="currentPage === 'VisaFreeApply'"
      :plan-id="currentPlanInfo.id"
      :free-attendance-id="freeAttendanceId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <VisaFreeRecord
      v-else-if="currentPage === 'VisaFreeRecord'"
      ref="visaFreeRecordRef"
      :plan-id="currentPlanInfo.id"
      :free-attendance-id="freeAttendanceId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { onLoad } from "@dcloudio/uni-app";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("VisaFreeApply");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const freeAttendanceId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "VisaFreeApply";
  mode.value = options?.mode || ModeEnum.ADD;
  freeAttendanceId.value = options?.freeAttendanceId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const visaFreeRecordRef = ref();
onShow(() => {
  visaFreeRecordRef.value?.init();
});

const title = computed(() => {
  const allTitle = {
    VisaFreeApply: "免签申请",
    VisaFreeRecord: "免签申请记录",
  };
  return allTitle[currentPage.value];
});
</script>
