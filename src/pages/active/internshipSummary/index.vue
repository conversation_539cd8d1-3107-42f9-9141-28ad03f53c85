<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <SummaryApply
      v-if="currentPage === 'SummaryApply'"
      :plan-id="currentPlanInfo.id"
      :summary-id="summaryId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <SummaryRecord
      v-else-if="currentPage === 'SummaryRecord'"
      ref="summaryRecordRef"
      :plan-id="currentPlanInfo.id"
      :summary-id="summaryId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("SummaryApply");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const summaryId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "SummaryApply";
  mode.value = options?.mode || ModeEnum.ADD;
  summaryId.value = options?.summaryId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const summaryRecordRef = ref();
onShow(() => {
  summaryRecordRef.value?.init();
});

const title = computed(() => {
  const allTitle = {
    SummaryApply: "实习总结",
    SummaryRecord: "实习总结",
  };
  return allTitle[currentPage.value];
});
</script>
