<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <MonthlyReportApply
      v-if="currentPage === 'MonthlyReportApply'"
      :plan-id="currentPlanInfo.id"
      :student-monthly-report-id="studentMonthlyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <MonthlyReportApply
      v-else-if="currentPage === 'AfterApply'"
      ref="monthlyReportRecordRef"
      :type="PageEnum.patch"
      :plan-id="currentPlanInfo.id"
      :student-monthly-report-id="studentMonthlyReportId" />
    <MonthlyReportRecord
      v-else-if="currentPage === 'MonthlyReportRecord'"
      :plan-id="currentPlanInfo.id"
      :student-monthly-report-id="studentMonthlyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum, PageEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("MonthlyReportApply");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const studentMonthlyReportId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "MonthlyReportApply";
  mode.value = options?.mode || ModeEnum.ADD;
  studentMonthlyReportId.value = options?.studentMonthlyReportId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const monthlyReportRecordRef = ref();
onShow(() => {
  monthlyReportRecordRef.value?.init();
});

const title = computed(() => {
  const allTitle = {
    MonthlyReportApply: "月报",
    AfterApply: "月报",
    MonthlyReportRecord: "月报记录",
  };
  return allTitle[currentPage.value];
});
</script>
