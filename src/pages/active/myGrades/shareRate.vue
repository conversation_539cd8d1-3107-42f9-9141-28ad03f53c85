<template>
  <ContentBox :title="title">
    <view p-24rpx>
      <view rounded-24rpx bg-white px-40rpx pb-80rpx pt-96rpx>
        <view m-auto h-500rpx w-500rpx>
          <up-qrcode
            m-auto
            :size="250"
            :val="qrcodeVal"
            @result="onResult"></up-qrcode>
        </view>
        <up-divider></up-divider>
        <view mt-40rpx flex flex-col gap-y-24rpx>
          <view class="text-32rpx text-#212121 font-500 leading-[44rpx]">
            使用帮助
          </view>
          <view flex items-center>
            <view class="round"></view>
            <view class="text">企业老师直接扫码二维码进行评分</view>
          </view>
          <view flex items-center>
            <view class="round"></view>
            <view class="text">学生可截图分享后给企业老师进行评分</view>
          </view>
        </view>
      </view>
      <view mt-56rpx flex items-center justify-around>
        <view v-for="(item, index) in list" :key="index">
          <image
            class="mb-16rpx block h-120rpx w-120rpx"
            :src="item.url"
            mode="scaleToFill" />
          <view class="text24rpx text-center text-#292929 leading-[34rpx]">
            {{ item.title }}
          </view>
        </view>
      </view>
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import { useApplicationStore } from "@/store";
import { checkRunEnvironment } from "@/utils";
import { initWxJsSdk } from "@/utils/wx-sdk";

const title = ref("企业带教师评分");
const qrcodeVal = ref<string>(
  "https://career.careermate.com.cn/internship_student#/pages/common/login/teacherLogin",
);

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const studentId = ref<number>();
const internshipPlanId = ref();
onLoad((options) => {
  studentId.value = options?.studentId || "";
  internshipPlanId.value = currentPlanInfo.value.id || "";
  qrcodeVal.value = `${qrcodeVal.value}?studentId=${studentId.value}&internshipPlanId=${internshipPlanId.value}`;
});

watch(
  () => currentPlanInfo.value,
  (val) => {
    internshipPlanId.value = val.id;
  },
);

const env = checkRunEnvironment();
const handleClick = async (item: { title: string; url: string }) => {
  if (item.title === "保存图片") {
    try {
      if (!base64Url.value) {
        uni.showToast({
          title: "图片未生成",
          icon: "none",
        });
        return;
      }

      if (env.isWechat) {
        // 微信浏览器环境
        try {
          // 初始化微信 JSSDK
          await initWxJsSdk(["saveImageToPhotosAlbum"]);

          // 将 base64 转换为本地图片
          const localId = await new Promise<string>((resolve, reject) => {
            wx.uploadImage({
              localId: base64Url.value as string,
              isShowProgressTips: 0,
              success: (res) => {
                resolve(res.serverId);
              },
              fail: reject,
            });
          });

          // 保存图片到相册
          await new Promise<void>((resolve, reject) => {
            wx.saveImageToPhotosAlbum({
              localId,
              success: () => {
                uni.showToast({
                  title: "保存成功",
                  icon: "success",
                });
                resolve();
              },
              fail: (err) => {
                console.error("保存图片失败:", err);
                uni.showToast({
                  title: "保存失败",
                  icon: "none",
                });
                reject(err);
              },
            });
          });
        } catch (error) {
          console.error("微信 JSSDK 保存图片失败:", error);
          // 如果 JSSDK 失败，尝试使用浏览器原生方式
          await saveImageByBrowser();
        }
      } else {
        // 其他浏览器环境
        await saveImageByBrowser();
      }
    } catch (error) {
      console.error("保存图片失败:", error);
      uni.showToast({
        title: "保存失败",
        icon: "none",
      });
    }
  }
};

/**
 * 使用浏览器原生方式保存图片
 */
const saveImageByBrowser = async () => {
  try {
    // 将 base64 转换为 Blob
    const base64Data = base64Url.value?.split(",")[1];
    if (!base64Data) {
      throw new Error("图片数据无效");
    }

    const byteString = atob(base64Data);
    const mimeString = "image/png";
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);

    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    const blob = new Blob([ab], { type: mimeString });
    const url = URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement("a");
    link.href = url;
    link.download = `评分二维码_${new Date().getTime()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    uni.showToast({
      title: "保存成功",
      icon: "success",
    });
  } catch (error) {
    console.error("浏览器保存图片失败:", error);
    throw error;
  }
};

const handleShare = () => {
  if (env.isMiniProgram) {
    const infoList = {
      action: "share", // H5消息的消息类型
      messageData: {
        shareTitle: "企业教师评分", // 分享标题
        shareImageUrl: "",
        sharePathUrl: "/pages/commom/login/teacherLogin",
      },
    };
    (wx as any).miniProgram.postMessage({ data: infoList });
  } else {
    console.log("不在微信小程序");
  }
};

const list = ref([
  // {
  //   title: "分享",
  //   url: "/static/images/share/ic_fenxiang.png",
  //   fun: handleShare,
  // },
  // {
  //   title: "扫一扫",
  //   url: "/static/images/share/ic_saoyisao.png",
  // },
  {
    title: "保存图片",
    url: "/static/images/share/ic_tupian.png",
    fun: handleClick,
  },
]);

const base64Url = ref<string>();
const onResult = (res) => {
  console.log("onResult===", res);
  base64Url.value = res;
  // base64Url.value = base64ToBlob(res);
};
</script>

<style lang="scss" scoped>
.round {
  @apply mr-16rpx h-12rpx w-12rpx rounded-full bg-#c7c7c7;
}

.text {
  @apply text-26rpx text-#616161 font-400 leading-[36rpx];
}
</style>
