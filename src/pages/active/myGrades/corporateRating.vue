<template>
  <ContentBox :title="title" height="480rpx" :background="bgImage">
    <view
      flex
      flex-col
      gap-y-24rpx
      p-24rpx
      :style="{
        background: `url(${bgImage}) no-repeat top center`,
        backgroundSize: '100%',
      }">
      <GradeHead
        :total-score="internshipAssessment?.company_teacher_score_score" />
      <DetailCard
        :item="internshipAssessment"
        :init-data="initData"
        :is-show-title="false"
        :is-show-tag="false" />
      <TutorInfo :detail="teacherInfo" />
      <RatingDetails v-if="evaluated" :data="selfRatingList" />
      <SelfRatingDetails v-else v-model="selfRatingList" />

      <up-button
        v-if="!evaluated"
        text="提交"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="onSubmit" />
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type { Indicators, TeacherInfo } from "@/api/myGrades/types";
import { CompanyTeacherApi } from "@/api";
import { enumApi } from "@/api/enums";
import { useLoading } from "@/hooks";
import bg_toubu3 from "@/static/images/bg_toubu3.png";
import { urlTobase64 } from "@/utils";

const bgImage = urlTobase64(bg_toubu3);
const title = ref("企业带教师评分");

const initData = ref([
  {
    label: "学号",
    key: "student_code",
  },
  {
    label: "班级",
    key: "class_name",
  },
  {
    label: "实习计划",
    key: "internship_plan_name",
  },
  {
    label: "实习类型",
    key: "internshipTypeLabel",
  },
]);

const selfRatingList = ref<Indicators[]>([]);
const evaluated = ref<boolean>(true);
const studentId = ref<number>();
const internshipPlanId = ref();
onLoad((options) => {
  studentId.value = options?.studentId;
  internshipPlanId.value = options?.internshipPlanId;
});

const { showLoading, hideLoading } = useLoading();
onMounted(() => {
  getScoreDetail();
});

const teacherInfo = ref<TeacherInfo>();
const internshipAssessment = ref();
const getScoreDetail = async () => {
  try {
    showLoading();
    const data = await CompanyTeacherApi.getScoreDetail();
    evaluated.value = data.evaluated;
    internshipAssessment.value = data.internship_assessment;
    internshipAssessment.value.internshipTypeLabel = enumApi
      .get("INTERNSHIP_TYPE")
      .findLabel(data.internship_assessment?.internship_method);
    selfRatingList.value = data.indicators;
    teacherInfo.value = {
      teacher_name: data.teacher_name,
      teacher_phone_number: data.teacher_phone,
    };
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};

const onSubmit = async () => {
  console.log("onSubmit", selfRatingList.value);
  try {
    showLoading();
    await CompanyTeacherApi.submitScore(selfRatingList.value);
  } catch (err) {
    console.error(err);
  } finally {
    hideLoading();
  }
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
