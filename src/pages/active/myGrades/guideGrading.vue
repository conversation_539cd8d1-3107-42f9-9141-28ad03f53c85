<template>
  <ContentBox :title="title" height="480rpx" :background="bgImage">
    <view
      flex
      flex-col
      gap-y-24rpx
      p-24rpx
      :style="{
        background: `url(${bgImage}) no-repeat top center`,
        backgroundSize: '100%',
      }">
      <GradeHead :total-score="detail?.internal_teacher_score_score" />
      <DetailCard
        :item="detail"
        :init-data="initData"
        :is-show-title="false"
        :is-show-tag="false" />
      <TutorInfo :detail="teacherInfo" />
      <RatingDetails :data="selfRatingList" />
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type {
  Indicators,
  InternshipAssessment,
  TeacherInfo,
} from "@/api/myGrades/types";
import { MyGradesApi } from "@/api";
import { enumApi } from "@/api/enums";
import { useLoading } from "@/hooks";
import bg_toubu2 from "@/static/images/bg_toubu2.png";
import { useApplicationStore } from "@/store";
import { urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const { showLoading, hideLoading } = useLoading();

const bgImage = urlTobase64(bg_toubu2);
const title = ref("指导教师评分详情");

const assessmentType = ref<string>();
onLoad((options) => {
  assessmentType.value = options?.assessmentType;
});

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const initData = ref([
  {
    label: "学号",
    key: "student_code",
  },
  {
    label: "班级",
    key: "grade_name",
  },
  {
    label: "实习计划",
    key: "internship_plan_name",
  },
  {
    label: "实习类型",
    key: "internshipTypeLabel",
  },
]);

const detail = ref<InternshipAssessment>();
const selfRatingList = ref<Indicators[]>();
const teacherInfo = ref<TeacherInfo>();
const assessStatus = ref<boolean>(true);
// 获取单项考核评分
const getScoreDetail = async () => {
  try {
    showLoading();
    const data = await MyGradesApi.getScoreDetail({
      internshipPlanId: currentPlanInfo.value.id,
      assessmentType: assessmentType.value,
    });
    assessStatus.value = data.assess_status;
    detail.value = data.internship_assessment;
    detail.value.internshipTypeLabel = enumApi
      .get("INTERNSHIP_TYPE")
      .findLabel(data.internship_assessment?.internship_method);
    teacherInfo.value = {
      teacher_name: data.teacher_name,
      teacher_phone_number: data.teacher_phone,
    };
    selfRatingList.value = data.indicators;
  } catch (err) {
    console.log(err);
  } finally {
    showLoading();
  }
};

const init = debounce(async () => {
  try {
    if (currentPlanInfo.value.id) {
      await getScoreDetail();
    }
  } catch (err) {
    console.log(err);
  }
}, 300);

watch(
  () => currentPlanInfo.value,
  () => {
    init();
  },
);

onShow(() => {
  init();
});
</script>

<style lang="scss" scoped></style>
