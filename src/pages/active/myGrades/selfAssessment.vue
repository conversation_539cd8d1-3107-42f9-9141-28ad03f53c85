<template>
  <ContentBox :title="title" height="480rpx" :background="bgImage">
    <view
      flex
      flex-col
      gap-y-24rpx
      p-24rpx
      :style="{
        background: `url(${bgImage}) no-repeat top center`,
        backgroundSize: '100%',
      }">
      <GradeHead :total-score="internship_assessment?.self_score_score" />
      <DetailCard
        :item="detail"
        :init-data="initData"
        :title="userStore.name"
        :is-show-title="false"
        :is-show-tag="false" />
      <RatingDetails v-if="isDetail" :data="selfRatingList" />
      <SelfRatingDetails v-else v-model="selfRatingList" />
      <up-button
        v-if="!isDetail"
        text="提交"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="onSubmit" />
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type { Indicators, InternshipPlan } from "@/api/myGrades/types";
import { MyGradesApi } from "@/api";
import { enumApi } from "@/api/enums";
import { ModeEnum } from "@/api/enums/value";
import { useLoading } from "@/hooks";
import bg_toubu2 from "@/static/images/bg_toubu2.png";
import { useApplicationStore, useUserStore } from "@/store";
import { Toast, urlTobase64 } from "@/utils";

const { showLoading, hideLoading } = useLoading();
const { currentPlanInfo } = storeToRefs(useApplicationStore());
const userStore = useUserStore();

const bgImage = urlTobase64(bg_toubu2);
const title = ref("自评");

const mode = ref<string>(ModeEnum.ADD);
onLoad((options) => {
  console.log("onLoad", currentPlanInfo.value);
  mode.value = options?.mode || ModeEnum.ADD;
});

const isDetail = computed(() => {
  return mode.value === ModeEnum.DETAIL;
});

const detail = ref<InternshipPlan>();
const initData = ref([
  {
    label: "学号",
    key: "code",
  },
  {
    label: "班级",
    key: "class_name",
  },
  {
    label: "实习计划",
    key: "name",
  },
  {
    label: "实习类型",
    key: "typeLabel",
  },
]);

const selfRatingList = ref<Indicators[]>();
const internship_assessment = ref();
// 获取学生自评信息
const getSelfScore = async () => {
  try {
    showLoading();
    const data = await MyGradesApi.getSelfScore({
      internshipPlanId: currentPlanInfo.value.id,
    });
    selfRatingList.value = data.indicators;
    internship_assessment.value = data.internship_assessment;
    detail.value = data.internship_plan;
    detail.value.class_name = userStore.class_name;
    detail.value.code = userStore.code as string;
    detail.value.typeLabel = enumApi
      .get("TEMPLATE_TYPE_ENUMS")
      .findLabel(detail.value.type);
    console.log("getSelfScore", selfRatingList.value);

    console.log(data);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

// const totalScore = computed(() => {
//   return selfRatingList.value?.reduce((acc, cur) => {
//     return acc + (cur?.score || 0);
//   }, 0);
// });

watch(
  () => currentPlanInfo.value.id,
  (val) => {
    val && getSelfScore();
  },
  {
    immediate: true,
  },
);

const onSubmit = async () => {
  console.log("onSubmit", selfRatingList.value);
  try {
    showLoading();
    await MyGradesApi.submitSelfScore(selfRatingList.value, {
      internshipPlanId: currentPlanInfo.value.id,
    });
    Toast("提交成功");
    uni.navigateBack();
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
