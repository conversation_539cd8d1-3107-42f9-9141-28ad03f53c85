<template>
  <ContentBox
    :title="title"
    height="460rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true"
    @change="onChange">
    <!-- <view relative mt-26rpx flex items-center px-30rpx pb-20rpx>
      <view flex items-baseline @click="showModal('title')">
        <view
          class="mr-8rpx h-42rpx text-30rpx text-#fff font-400 leading-[42rpx]">
          {{ formInfo.title }}
        </view>

      </view>
    </view> -->
    <view p-24rpx pt-80rpx>
      <MyGradesInfo :score-info="scoreInfo" />
    </view>
  </ContentBox>
  <up-picker
    :show="showPicker"
    :columns="columns"
    confirm-color="#D63C38"
    key-name="name"
    @cancel="onCancel"
    @confirm="onSure"></up-picker>
</template>

<script lang="ts" setup>
import type { StudentInternshipListRes } from "@/api/internshipProgram/types";
import { MyGradesApi } from "@/api";
import { useLoading } from "@/hooks";
import usePicker from "@/hooks/usePicker";
import bg_toubu1 from "@/static/images/bg_toubu1.png";

import useApplicationStore from "@/store/modules/application";
import { setToken, urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const bgImage = urlTobase64(bg_toubu1);
const title = ref("我的成绩");

const formInfo = ref<any>({
  title: "选择实习计划",
});

const token = ref("");
onMounted(async () => {
  if (token.value) {
    setToken(token.value);
  }
  await init();
});
onLoad((options) => {
  console.log("接收到的参数:", options);
  token.value = options?.token;
});

const { setCurrentPlanInfo } = useApplicationStore();
const getStudentInternshipList = async () => {
  try {
    showLoading();
    // const data = await InternshipProgramApi.getStudentInternshipList();
    // columns.value[0] = data;
    // setCurrentPlanInfo(data[0]);
    getScore();
    // if (data.length) {
    //   formInfo.value.title = data[0].name as string;
    // }
  } catch (error) {
    console.error("Error fetching internship list:", error);
  } finally {
    hideLoading();
  }
};

const init = debounce(async () => {
  try {
    formInfo.value.title = currentPlanInfo.value.name || "选择实习计划";
    await getStudentInternshipList();
  } catch (err) {
    console.log(err);
  }
}, 300);

// 移除watch监听器，只在页面加载时获取数据

const onSure = (e) => {
  onConfirm(e);
  setCurrentPlanInfo(e.value[0]);
};
const { showPicker, showModal, onCancel, onConfirm } = usePicker(formInfo);
const columns = ref<StudentInternshipListRes[][]>([]);

// 获取考核成绩
const scoreInfo = ref();
const getScore = async () => {
  try {
    const data = await MyGradesApi.getScore({
      internshipPlanId: currentPlanInfo.value.id,
    });
    scoreInfo.value = data;
  } catch (err: any) {
    console.log("getScore====err", err);
    scoreInfo.value = err.data;
  }
};

const onChange = (_data: any) => {
  init();
};
</script>

<style lang="scss" scoped></style>
