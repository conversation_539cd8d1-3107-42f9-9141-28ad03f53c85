<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <NewFeedback
      v-if="currentPage === 'NewFeedback'"
      :plan-id="currentPlanInfo.id"
      :feedback-id="feedbackId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <FeedbackRecord
      v-else-if="currentPage === 'FeedbackRecord'"
      ref="feedbackRecordRef"
      :plan-id="currentPlanInfo.id"
      :feedback-id="feedbackId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum } from "@/api/enums/value";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64("/static/images/smallNavBarBg.png");

const currentPage = ref<string>("NewFeedback");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const feedbackId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "NewFeedback";
  mode.value = options?.mode || ModeEnum.ADD;
  feedbackId.value = options?.feedbackId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const feedbackRecordRef = ref();
onShow(() => {
  feedbackRecordRef.value?.getFeedbackList();
});

const title = computed(() => {
  const allTitle = {
    NewFeedback: "实习反馈",
    FeedbackRecord: "反馈记录",
  };
  return allTitle[currentPage.value];
});
</script>
