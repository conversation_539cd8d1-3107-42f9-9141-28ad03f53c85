<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <ClockIn
      v-if="currentPage === 'ClockIn'"
      ref="clockInRef"
      :plan-id="currentPlanInfo.id" />
    <Record
      v-else-if="currentPage === 'Record'"
      ref="recordRef"
      :plan-id="currentPlanInfo.id" />
    <Replenish
      v-else-if="currentPage === 'Replenish'"
      :plan-id="currentPlanInfo.id" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import { useApplicationStore } from "@/store";
import { urlTobase64 } from "@/utils";
import { onLoad } from "@dcloudio/uni-app";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("ClockIn");
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "ClockIn";
});

const title = computed(() => {
  const allTitle = {
    ClockIn: "签到",
    Record: "签到记录",
    Replenish: "补签",
  };
  return allTitle[currentPage.value];
});

const clockInRef = ref();
const recordRef = ref();
// onShow(() => {
//   if (currentPage.value === "ClockIn") {
//     clockInRef.value?.init();
//   } else if (currentPage.value === "Record") {
//     recordRef.value?.init();
//   }
// });
</script>
