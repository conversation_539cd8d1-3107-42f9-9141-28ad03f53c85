<template>
  <ContentBox
    title="实习结束申请"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true"
    @change="onChange">
    <view p-20rpx>
      <!-- 空列表 -->
      <view
        v-if="!list?.length"
        class="rounded-24rpx bg-white px-40rpx pb-96rpx pt-122rpx">
        <up-empty
          width="600rpx"
          height="100%"
          :icon="iconStatus"
          text="暂无实习岗位">
          <view class="mt-50rpx w-630rpx flex flex-col gap-y-32rpx">
            <up-button
              text="实习结束"
              shape="circle"
              :disabled="disabledApply"
              color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
              @click="onFinish" />
          </view>
        </up-empty>
      </view>
      <!-- list -->
      <view v-else>
        <view v-for="(item, index) in list" :key="index">
          <CardItem
            :item="item"
            :init-data="initData"
            :title="`${userStore.name}的实习结束申请`">
            <template #default>
              <view flex justify-end gap-x-16rpx>
                <up-button
                  v-if="item.audit_status === StatusEnum.pending"
                  :custom-style="{
                    border: '1px solid #9B9B9B',
                    color: '#9B9B9B',
                    width: '155rpx',
                    height: '56rpx',
                  }"
                  text="撤销申请"
                  shape="circle"
                  @click="revoke(item)"></up-button>
                <up-button
                  v-else-if="
                    item.audit_status === StatusEnum.rejected ||
                    item.audit_status === StatusEnum.revoked
                  "
                  :custom-style="customStyle"
                  color="#FFE7E6"
                  text="重新提交"
                  shape="circle"
                  @click="onFinish"></up-button>
                <up-button
                  v-else-if="item.audit_status === StatusEnum.approved"
                  :custom-style="customStyle"
                  color="#FFE7E6"
                  text="实习转就业"
                  shape="circle"
                  @click="toEmployment(item)"></up-button>
                <up-button
                  :custom-style="{
                    width: '155rpx',
                    height: '56rpx',
                  }"
                  text="结束详情"
                  shape="circle"
                  color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
                  @click="goDetail(item)"></up-button>
              </view>
            </template>
          </CardItem>
        </view>
      </view>
    </view>
    <FinishModal v-model="isShowModal" @confirm="onConfirm" />
  </ContentBox>
</template>

<script lang="ts" setup>
import type { IApplication } from "@/api/internshipProgram/types";
import { InternshipProgramApi } from "@/api";
import { StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import { useUserStore } from "@/store";
import useApplicationStore from "@/store/modules/application";
import { Toast, urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const userStore = useUserStore();

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);
const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});

const disabled = computed(() => {
  return false;
});
const iconStatus = computed(() => {
  return disabled.value
    ? "/static/images/tip/img_kong.png"
    : "/static/images/tip/ic_finish.png";
});

// 获取实习计划id
const id = ref<number>(1);
const onChange = (val) => {
  console.log("onChange=====", val);
  id.value = val;
};

const { showLoading, hideLoading } = useLoading();

const disabledApply = ref<boolean>(false);
// 获取实习计划变更申请列表
const getApplicationEndList = async () => {
  const data = await InternshipProgramApi.getApplicationEndList({
    internshipPlanId: currentPlanInfo.value.id,
  });
  disabledApply.value = !data.can_apply;
  list.value = data.application?.map((item) => {
    return {
      ...item,
      time: `${item.expected_start_date} - ${item.expected_end_date}`,
    };
  });
  console.log(data);
};

const init = debounce(async () => {
  // await checkCanEndApplication();
  showLoading();
  await getApplicationEndList();
  hideLoading();
}, 500);

watch(
  () => currentPlanInfo.value,
  () => {
    init();
  },
);

onShow(() => {
  if (currentPlanInfo.value) {
    init();
  }
});

// 创建学生的实习计划结束申请
const createEndInternshipApplication = async () => {
  const res = await InternshipProgramApi.createEndInternshipApplication({
    internshipPlanId: currentPlanInfo.value.id,
  });
  console.log(res);
};

// 撤销实习结束申请
const revokeEndApplication = async (item) => {
  const data = await InternshipProgramApi.revokeEndApplication({
    endApplicationId: item.id,
  });
  console.log(data);
};

const list = ref<IApplication[]>();

const initData = ref([
  {
    label: "单位名称",
    key: "company_name",
  },
  {
    label: "岗位",
    key: "position_name",
  },
  {
    label: "实习时间",
    key: "time",
  },
]);

// 实习结束弹框
const isShowModal = ref<boolean>(false);
const onFinish = () => {
  isShowModal.value = true;
};
const onConfirm = async () => {
  showLoading();
  await createEndInternshipApplication();
  await getApplicationEndList();
  hideLoading();
};

// 撤销申请
const { showModal } = useModal();
const revoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      showLoading();
      await revokeEndApplication(item);
      await getApplicationEndList();
      Toast("撤销成功");
      hideLoading();
    }
  } catch (err) {
    console.log(err);
  }
};

const toEmployment = (item) => {
  uni.navigateTo({
    url: `/pages/active/employment/employmentApply?application_id=${item.application_id}&internship_plan_id=${item.internship_plan_id}`,
  });
};

const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/internshipFinishApplication/detail?endApplicationId=${item.id}`,
  });
};
</script>

<style lang="scss" scoped></style>
