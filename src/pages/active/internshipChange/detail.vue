<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <DetailCard
      :item="detail"
      :init-data="initData"
      :title="`${userStore.name}的实习变更申请`">
      <template #match>
        <Match />
      </template>
      <template #time>
        <view between items-center py-20rpx>
          <view flex items-center>
            <view
              class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
            <view>上班时间</view>
          </view>
          <view></view>
        </view>
        <up-line color="#f1f1f1" dashed></up-line>
        <view flex items-center pt-20rpx>
          <view flex items-center>
            <view
              class="bg-linear-red mr-8rpx h-12rpx w-12rpx rounded-full"></view>
            <view>下班时间</view>
          </view>
          <view></view>
        </view>
      </template>
    </DetailCard>

    <AttachmentDetail :form-data="formData" />

    <Process :list="stepsList" />

    <!-- 审批中展示 -->
    <view
      v-if="detail.audit_status === StatusEnum.pending"
      class="mt-50rpx flex flex-col gap-y-32rpx">
      <up-button
        text="撤销"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="onRevoke" />
    </view>
    <!-- 驳回展示 -->
    <up-button
      v-if="
        detail.audit_status === StatusEnum.rejected ||
        detail.audit_status === StatusEnum.revoked
      "
      text="重新提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      @click="onResubmit" />
  </view>
</template>

<script lang="ts" setup>
import type { GetApprovalFlowRes } from "@/api/internshipProgram/types";
import type { IAttachmentInfoForm } from "@/components/internshipApplication/type";
import { InternshipProgramApi } from "@/api";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import { useUserStore } from "@/store";
import { Toast } from "@/utils";

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

const changeApplicationId = ref<number>(1);
onLoad((options) => {
  changeApplicationId.value = Number(options?.changeApplicationId);
  console.log("onLoad===", options);
});

onMounted(async () => {
  showLoading();
  await Promise.all([getChangeApplication(), getApprovalFlow()]);
  hideLoading();
});

// 获取实习申请详情
const getChangeApplication = async () => {
  try {
    const data = await InternshipProgramApi.getChangeApplication({
      changeApplicationId: changeApplicationId.value,
    });

    detail.value = {
      ...data.change_application,
      company_change_info: data.company_change_info,
      position_change_info: data.position_change_info,
      internship_change_info: data.internship_change_info?.map((item: any) => {
        if (item.after === true) {
          item.after = "是";
        } else if (item.after === false) {
          item.after = "否";
        }

        if (item.before === true) {
          item.before = "是";
        } else if (item.before === false) {
          item.before = "否";
        }
        return {
          ...item,
        };
      }),
    };
    formData.value.agreement_attachment = JSON.parse(
      data.after_application?.agreement_attachment || "",
    );
    formData.value.insurance_attachment = JSON.parse(
      data.after_application?.insurance_attachment || "",
    );
    formData.value.other_attachment = JSON.parse(
      data.after_application?.other_attachment || "",
    );
  } catch (err) {
    console.log(err);
  }
};

// 撤销实习计划申请
const cancelChangeApplication = async () => {
  const res = await InternshipProgramApi.cancelChangeApplication({
    changeApplicationId: changeApplicationId.value,
  });
  console.log(res);
};

// 获取实习计划变更申请审批流程
const getApprovalFlow = async () => {
  try {
    const data = await InternshipProgramApi.getApprovalFlow({
      changeApplicationId: changeApplicationId.value,
    });
    console.log("getApprovalFlow=====", data);

    stepsList.value = data;
    console.log("getApprovalFlow", data);
  } catch (err) {
    console.log(err);
  }
};

// 详情页
const detail = ref<any>({});
const formData = ref<IAttachmentInfoForm>({});
const initData = ref([
  {
    label: "审批编号",
    key: "code",
  },
  {
    label: "提交时间",
    key: "created_at",
  },
  {
    label: "上一个实习结束时间",
    key: "before_internship_actual_end_date",
  },
  {
    label: "企业信息",
    key: "company_change_info",
    type: "table",
    columns: [
      { title: "变更项", key: "name", width: "30%" },
      { title: "变更前", key: "before", width: "35%" },
      { title: "变更后", key: "after", width: "35%" },
    ],
  },
  {
    label: "岗位信息",
    type: "slot",
    slotName: "match",
  },
  {
    key: "position_change_info",
    type: "table",
    columns: [
      { title: "变更项", key: "name", width: "30%" },
      { title: "变更前", key: "before", width: "35%" },
      { title: "变更后", key: "after", width: "35%" },
    ],
  },
  {
    label: "实习变更",
    key: "internship_change_info",
    type: "table",
    columns: [
      { title: "变更项", key: "name", width: "30%" },
      { title: "变更前", key: "before", width: "35%" },
      { title: "变更后", key: "after", width: "35%" },
    ],
  },
]);

// 审批流程
const stepsList = ref<GetApprovalFlowRes[]>([]);

const { showModal } = useModal();
const onRevoke = async () => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      await cancelChangeApplication();
      Toast("撤销成功");
      uni.navigateBack();
    }
  } catch (err) {
    console.log(err);
  }
};
const onResubmit = async () => {
  await showModal("您确定要重新提交申请吗？");
  uni.redirectTo({
    url: `/pages/active/internshipChange/application?mode=${ModeEnum.EDIT}&changeApplicationId=${changeApplicationId.value}`,
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
