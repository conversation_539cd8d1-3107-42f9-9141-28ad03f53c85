<template>
  <ContentBox
    title="实习变更申请"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <view p-20rpx>
      <!-- 空列表 -->
      <view
        v-if="!list?.length"
        class="rounded-24rpx bg-white px-40rpx pb-96rpx pt-122rpx">
        <up-empty
          width="600rpx"
          height="100%"
          :icon="iconStatus"
          text="暂无实习岗位">
          <view class="mt-50rpx w-630rpx flex flex-col gap-y-32rpx">
            <up-button
              text="实习变更"
              shape="circle"
              :disabled="disabledApply"
              color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
              @click="goApplication(ModeEnum.ADD)" />
          </view>
        </up-empty>
      </view>
      <!-- list -->
      <template v-else>
        <view v-for="(item, index) in list" :key="index">
          <CardItem
            :item="item"
            :init-data="initData"
            :title="`${userStore.name}的实习变更申请`"
            @finish="onFinish">
            <template #footer>
              <view
                class="mt-24rpx flex flex-col gap-y-20rpx rounded-24rpx bg-#f8fafa p-24rpx">
                <view class="text-24rpx text-#616161 font-400 leading-34rpx">
                  实习单位
                </view>
                <view
                  class="flex items-center text-26rpx font-400 leading-34rpx">
                  <view
                    class="bg-linear-blue mr-16rpx h-10rpx w-10rpx rounded-full"></view>
                  <view class="text-#616161">变更前：</view>
                  <view class="text-#212121">
                    {{ item.change_before_company_name }}
                  </view>
                </view>
                <view
                  class="flex items-center text-26rpx font-400 leading-34rpx">
                  <view
                    class="bg-linear-red mr-16rpx h-10rpx w-10rpx rounded-full"></view>
                  <view class="text-#616161">变更后：</view>
                  <view class="text-#212121">
                    {{ item.change_after_company_name }}
                  </view>
                </view>
              </view>
              <view
                class="mt-24rpx flex flex-col gap-y-20rpx rounded-24rpx bg-#f8fafa p-24rpx">
                <view class="text-24rpx text-#616161 font-400 leading-34rpx">
                  岗位名称
                </view>
                <view
                  class="flex items-center text-26rpx font-400 leading-34rpx">
                  <view
                    class="bg-linear-blue mr-16rpx h-10rpx w-10rpx rounded-full"></view>
                  <view class="text-#616161">变更前：</view>
                  <view class="text-#212121">
                    {{ item.change_before_position }}
                  </view>
                </view>
                <view
                  class="flex items-center text-26rpx font-400 leading-34rpx">
                  <view
                    class="bg-linear-red mr-16rpx h-10rpx w-10rpx rounded-full"></view>
                  <view class="text-#616161">变更后：</view>
                  <view class="text-#212121">
                    {{ item.change_after_position }}
                  </view>
                </view>
              </view>
            </template>
            <template #default>
              <view flex justify-end gap-x-16rpx>
                <up-button
                  v-if="item.audit_status === StatusEnum.pending"
                  :custom-style="{
                    border: '1px solid #9B9B9B',
                    color: '#9B9B9B',
                    width: '155rpx',
                    height: '56rpx',
                  }"
                  text="撤销申请"
                  shape="circle"
                  @click="revoke(item)"></up-button>
                <up-button
                  v-else-if="
                    item.audit_status === StatusEnum.rejected ||
                    item.audit_status === StatusEnum.revoked
                  "
                  :custom-style="customStyle"
                  color="#FFE7E6"
                  text="重新提交"
                  shape="circle"
                  @click="goApplication(ModeEnum.EDIT, item)"></up-button>
                <up-button
                  :custom-style="{
                    width: '155rpx',
                    height: '56rpx',
                  }"
                  text="变更详情"
                  shape="circle"
                  color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
                  @click="goDetail(item)"></up-button>
              </view>
            </template>
          </CardItem>
        </view>
        <view class="button">
          <up-button
            text="实习变更"
            shape="circle"
            :disabled="disabledApply"
            color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
            @click="goApplication(ModeEnum.ADD)" />
        </view>
      </template>
    </view>
    <FinishModal v-model="isShowModal" />
  </ContentBox>
</template>

<script lang="ts" setup>
import type { ChangeApplicationList } from "@/api/internshipProgram/types";
import { InternshipProgramApi } from "@/api";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import { useUserStore } from "@/store";
import useApplicationStore from "@/store/modules/application";
import { Toast, urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

const bgImage = urlTobase64(smallNavBarBg);
const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});

const iconStatus = computed(() => {
  return disabledApply.value
    ? "/static/images/tip/img_kong.png"
    : "/static/images/tip/ic_change.png";
});

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const initData = ref([
  {
    label: "提交时间",
    key: "created_at",
  },
  {
    label: "上一个实习结束日期",
    key: "before_internship_actual_end_date",
  },
]);

// 检查学生是否可以申请实习计划
const disabledApply = ref<boolean>(false);
const list = ref<ChangeApplicationList[]>();
// 获取实习申请列表
const getChangeApplicationList = async () => {
  const data = await InternshipProgramApi.getChangeApplicationList({
    internshipPlanId: currentPlanInfo.value.id,
  });
  list.value = data.applications;
  disabledApply.value = !data.can_apply;
  console.log(data);
};

const init = debounce(async () => {
  try {
    showLoading();
    getChangeApplicationList();
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
}, 500);

watch(
  () => currentPlanInfo.value,
  () => {
    init();
  },
);

onShow(() => {
  if (currentPlanInfo.value) {
    init();
  }
});

// 撤销实习计划申请
const cancelChangeApplication = async (item) => {
  showLoading();
  await InternshipProgramApi.cancelChangeApplication({
    changeApplicationId: item.id,
  });
  hideLoading();
};

// 实习结束弹框
const isShowModal = ref<boolean>(false);
const onFinish = () => {
  isShowModal.value = true;
};

// 撤销申请
const { showModal } = useModal();
const revoke = async (item) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      await cancelChangeApplication(item);
      Toast("撤销成功");
      getChangeApplicationList();
    }
  } catch (err) {
    console.log(err);
  }
};

const goApplication = (type, item?) => {
  console.log("goDetail", item);
  if (item) {
    uni.navigateTo({
      url: `/pages/active/internshipChange/application?mode=${type}&changeApplicationId=${item?.id}`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/active/internshipChange/application?mode=${type}`,
    });
  }
};

const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/active/internshipChange/detail?changeApplicationId=${item.id}`,
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
.button {
  ::v-deep .u-button {
    height: 88rpx !important;
    margin-top: 24rpx;

    .u-button__text {
      font-size: 32rpx !important;
    }
  }
}
</style>
