<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <EmploymentRecord v-if="list?.length" :list="list" />
    <view v-else my-300rpx>
      <Empty />
    </view>
    <view between gap-x-24rpx px-20rpx>
      <up-button
        :custom-style="{
          border: '1px solid #D63C38',
          color: '#D63C38',
          width: '50%',
          height: '88rpx',
          fontSize: '32rpx',
        }"
        text="实习转就业"
        color="#FFE7E6"
        shape="circle"
        :disabled="disabledApply"
        @click="goApplay"></up-button>
      <up-button
        :custom-style="{
          width: '50%',
          height: '88rpx',
          fontSize: '32rpx',
        }"
        text="自主上报就业"
        shape="circle"
        :disabled="disabledApply"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="goApplay('self')"></up-button>
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type { Employment } from "@/api/employmentReport/types";
import { EmploymentReportApi } from "@/api";
import { useLoading } from "@/hooks";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";

const bgImage = urlTobase64(smallNavBarBg);
const title = ref<string>("就业上报");

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const list = ref<Employment[]>();
const disabledApply = ref<boolean>(true);
const getEmploymentReportList = async () => {
  try {
    showLoading();
    const data = await EmploymentReportApi.getEmploymentReportList({
      internshipPlanId: currentPlanInfo.value.id,
    });
    disabledApply.value = !data.can_apply;
    list.value = data.employment?.map((item) => {
      return {
        ...item,
        time: `${item.start_date} - ${item.end_date}`,
      };
    });
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

watch(
  () => currentPlanInfo.value.id,
  (val) => {
    val && getEmploymentReportList();
  },
  {
    immediate: true,
  },
);

const goApplay = (mode?: string) => {
  uni.navigateTo({
    url: `/pages/active/employment/employmentApply?mode=${mode}&internship_plan_id=${currentPlanInfo.value.id}`,
  });
};
</script>

<!-- <script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style> -->
