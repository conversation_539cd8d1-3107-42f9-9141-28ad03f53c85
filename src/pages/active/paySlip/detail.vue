<template>
  <ContentBox :title="title">
    <view
      class="flex items-center justify-between px-24rpx py-30rpx text-#212121">
      <view class="text-30rpx leading-[42rpx]">{{ currentPlanInfo.name }}</view>
      <view class="center text-28rpx leading-[40rpx]">
        <image
          class="h-32rpx w-32rpx"
          src="/static/images/ic_riqi.png"
          mode="scaleToFill" />
        <view>{{ detail?.salary_year }}/{{ detail?.salary_month }}</view>
      </view>
    </view>
    <view class="flex flex-col gap-y-24rpx p-24rpx">
      <PaySlipDesc v-model="paySlipDescData" />
      <PaySlipInfo v-model="paySlipInfoData" />
      <up-button
        text="提交"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        @click="onSbmit"></up-button>
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type { GetStudentSalaryDetailRes } from "@/api/paySlip/types";
import type { IPaySlipDesc, IPaySlipInfo } from "@/components/paySlip/type";
import { PaySlipApi } from "@/api";
import { useLoading } from "@/hooks";
import useApplicationStore from "@/store/modules/application";

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const title = ref("工资单填写");

const studentSalaryId = ref<number>();
onLoad((options) => {
  console.log("接收到的参数:", options);
  studentSalaryId.value = options?.studentSalaryId;
});

onMounted(() => {
  getStudentSalaryDetail();
});

const detail = ref<GetStudentSalaryDetailRes>();
// 获取工资单详情
const getStudentSalaryDetail = async () => {
  const data = await PaySlipApi.getStudentSalaryDetail({
    studentSalaryId: studentSalaryId.value,
  });
  detail.value = data;
  Object.assign(paySlipDescData.value, {
    salary: data.salary,
    company_name: data.company_name,
    position_name: data.position_name,
    work_hours: data.work_hours,
  });
  paySlipInfoData.value.attachment =
    data.attachment && JSON.parse(data.attachment);
  console.log("getStudentSalaryList", data);
};

const paySlipDescRef = ref();
const paySlipInfoRef = ref();

const paySlipDescData = ref<IPaySlipDesc>({
  salary: "",
  company_name: "",
  position_name: "",
  work_hours: "",
});
const paySlipInfoData = ref<IPaySlipInfo>({
  attachment: [],
});
const onSbmit = () => {
  console.log("onSbmit==paySlipDescData", paySlipDescData.value);
  console.log("onSbmit==paySlipInfoData", paySlipInfoData.value);

  Promise.all([
    paySlipDescRef.value?.formRef.validate(),
    paySlipInfoRef.value?.formRef.validate(),
  ])
    .then(async (res) => {
      try {
        showLoading();
        const allInfo: any = {
          ...paySlipDescData.value,
          attachment: JSON.stringify(paySlipInfoData.value.attachment),
        };
        console.log("All forms validated successfully", allInfo);
        await PaySlipApi.submitStudentSalary(allInfo, {
          salaryId: studentSalaryId.value,
        });
      } catch (err) {
        console.log(err);
      } finally {
        hideLoading();
      }
      uni.navigateBack();
    })
    .catch((errors) => {
      // 处理验证错误
      console.error("Validation failed", errors);
    });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;
  margin-top: 24rpx;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
