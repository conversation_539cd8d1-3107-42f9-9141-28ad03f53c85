<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <LeaveApply
      v-if="currentPage === 'LeaveApply'"
      :plan-id="currentPlanInfo.id"
      :leave-attendance-id="leaveAttendanceId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <LeaveRecord
      v-else-if="currentPage === 'LeaveRecord'"
      ref="leaveRecordRef"
      :plan-id="currentPlanInfo.id"
      :leave-attendance-id="leaveAttendanceId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("LeaveRecord");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const leaveAttendanceId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "LeaveApply";
  mode.value = options?.mode || ModeEnum.ADD;
  leaveAttendanceId.value = options?.leaveAttendanceId;
});

const leaveRecordRef = ref();
onShow(() => {
  leaveRecordRef.value?.init();
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const title = computed(() => {
  const allTitle = {
    LeaveApply: "请假申请",
    LeaveRecord: "请假记录",
  };
  return allTitle[currentPage.value];
});
</script>
