<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <DailyReportApply
      v-if="currentPage === 'DailyReportApply'"
      :plan-id="currentPlanInfo.id"
      :student-daily-report-id="studentDailyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <DailyReportApply
      v-else-if="currentPage === 'AfterApply'"
      :type="PageEnum.patch"
      :plan-id="currentPlanInfo.id"
      :student-daily-report-id="studentDailyReportId" />
    <DailyReportRecord
      v-else-if="currentPage === 'DailyReportRecord'"
      ref="dailyReportRecordRef"
      :plan-id="currentPlanInfo.id"
      :student-daily-report-id="studentDailyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum, PageEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("DailyReportApply");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const studentDailyReportId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "DailyReportApply";
  mode.value = options?.mode || ModeEnum.ADD;
  studentDailyReportId.value = options?.studentDailyReportId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const dailyReportRecordRef = ref();
onShow(() => {
  dailyReportRecordRef.value?.init();
});

const title = computed(() => {
  const allTitle = {
    DailyReportApply: "日报",
    AfterApply: "日报",
    DailyReportRecord: "日报记录",
  };
  return allTitle[currentPage.value];
});
</script>
