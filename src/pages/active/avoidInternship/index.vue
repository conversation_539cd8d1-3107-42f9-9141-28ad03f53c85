<template>
  <ContentBox
    title="免实习-实习申请"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true"
    @change="onChange">
    <view p-20rpx>
      <!-- 筛选条件展示 -->
      <view v-if="searchStr" mb-24rpx flex items-center justify-between>
        <view class="text-26rpx text-#9B9B9B font-400 leading-[36rpx]">
          筛选条件：{{ searchStr }}
        </view>
        <view class="i-ep-close text-#9B9B9B" @click="clearSearch" />
      </view>

      <!-- 空列表 -->
      <view
        v-if="!list?.length"
        class="rounded-24rpx bg-white px-40rpx pb-96rpx pt-122rpx">
        <up-empty
          width="600rpx"
          height="100%"
          icon="/static/images/tip/img_kong.png"
          text="暂无实习岗位">
          <view class="mt-50rpx w-630rpx flex flex-col gap-y-32rpx">
            <up-button
              :custom-style="{
                border: '1px solid #D63C38',
                color: '#D63C38',
              }"
              :disabled="disabledApply"
              text="免实习申请"
              color="#FFE7E6"
              shape="circle"
              @click="goFreeApplication(ModeEnum.ADD)" />
          </view>
        </up-empty>
      </view>

      <view v-for="(item, index) in list" :key="index">
        <CardItem
          :item="item"
          :init-data="initData"
          :is-show-image="false"
          :title="`${userStore.name}的免实习申请`">
          <view flex gap-x-16rpx>
            <up-button
              v-if="item.audit_status === StatusEnum.pending"
              :custom-style="{
                border: '1px solid #9B9B9B',
                color: '#9B9B9B',
                width: '155rpx',
                height: '56rpx',
              }"
              text="撤销申请"
              shape="circle"
              @click="onRevoke(item)"></up-button>
            <up-button
              v-else-if="
                item.audit_status === StatusEnum.rejected ||
                item.audit_status === StatusEnum.revoked
              "
              :custom-style="customStyle"
              color="#FFE7E6"
              text="重新提交"
              shape="circle"
              @click="goFreeApplication(ModeEnum.EDIT, item)"></up-button>
            <up-button
              :custom-style="{
                width: '155rpx',
                height: '56rpx',
              }"
              text="实习详情"
              shape="circle"
              color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
              @click="goDetail(item)"></up-button>
          </view>
        </CardItem>
      </view>
    </view>
  </ContentBox>
</template>

<script lang="ts" setup>
import type { GetApplicationFreeList } from "@/api/internshipProgram/types";
import { InternshipProgramApi } from "@/api";
import { ModeEnum, StatusEnum } from "@/api/enums/value";
import { useLoading, useModal } from "@/hooks";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import { useUserStore } from "@/store";
import useApplicationStore from "@/store/modules/application";
import { Toast, urlTobase64 } from "@/utils";
import { debounce } from "lodash-es";

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();

const bgImage = urlTobase64(smallNavBarBg);
const customStyle = reactive({
  border: "1px solid #D63C38",
  color: "#D63C38",
  width: "155rpx",
  height: "56rpx",
});

const { initSearchValueList } = useApplicationStore();
const { currentPlanInfo, searchValueList } = storeToRefs(useApplicationStore());
const list = ref<GetApplicationFreeList[]>();

// 检查是否可以申请免实习
const disabledApply = ref<boolean>(true);

// 获取实习申请列表
const getApplicationFreeList = async () => {
  const data = await InternshipProgramApi.getApplicationFreeList({
    internshipPlanId: currentPlanInfo.value.id,
  });
  list.value = data.applications;
  disabledApply.value = !data.can_apply;
};

const initData = ref([
  {
    label: "申请原因",
    key: "detail",
  },
  {
    label: "申请时间",
    key: "created_at",
  },
]);

const init = debounce(async () => {
  try {
    showLoading();
    await getApplicationFreeList();
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
}, 300);

watch(
  () => currentPlanInfo.value,
  () => {
    init();
  },
);

onShow(() => {
  if (currentPlanInfo.value) {
    init();
  }
});

// 筛选参数处理
const searchStr = ref<string>("");
const onChange = (_data: any) => {
  searchStr.value = searchValueList.value?.join("、");
};
const clearSearch = () => {
  searchStr.value = "";
  initSearchValueList();
};

// 撤销免实习申请
const revokeFreeApplication = async (item: GetApplicationFreeList) => {
  const res = await InternshipProgramApi.revokeFreeApplication({
    freeInternshipId: item.id,
  });
  console.log(res);
};

// 撤销申请
const { showModal } = useModal();
const onRevoke = async (item: GetApplicationFreeList) => {
  try {
    const res: any = await showModal("您确定要撤销申请吗？");
    if (res.confirm) {
      await revokeFreeApplication(item);
      Toast("撤销成功");
      getApplicationFreeList();
    }
  } catch (err) {
    console.log(err);
  }
};

const goFreeApplication = (type: string, item?: GetApplicationFreeList) => {
  console.log("goDetail", item);
  if (item) {
    uni.navigateTo({
      url: `/pages/active/avoidInternship/application?mode=${type}&freeInternshipId=${item?.id}`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/active/avoidInternship/application?mode=${type}`,
    });
  }
};

// 实习详情
const goDetail = (item: GetApplicationFreeList) => {
  uni.navigateTo({
    url: `/pages/active/avoidInternship/detail?freeInternshipId=${item.id}`,
  });
};
</script>

<style lang="scss" scoped></style>
