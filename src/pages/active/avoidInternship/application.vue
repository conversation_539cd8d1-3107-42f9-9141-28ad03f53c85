<template>
  <view
    class="bg-white px-30rpx pb-20rpx pt-30rpx text-30rpx text-#212121 leading-[42rpx]">
    {{ currentPlanInfo.name }}
  </view>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <InternshipReason ref="internshipReasonoRef" v-model="formData" />
    <UploadImg ref="uploadImgRef" v-model="uploadImgData" />
    <view
      v-if="isEdit && detailInfo?.audit_remark"
      class="rounded-24rpx bg-#FFE7E6 px-24rpx py-32rpx text-28rpx text-#D63C38 leading-[40rpx]">
      驳回原因：{{ detailInfo.audit_remark }}
    </view>
    <up-button
      text="提交"
      shape="circle"
      color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
      :disabled="disabledApply"
      @click="onSubmit"></up-button>
  </view>
</template>

<script lang="ts" setup>
import type { GetApplicationFreeDetailRes } from "@/api/internshipProgram/types";
import type {
  IInternshipReason,
  IUploadImgForm,
} from "@/components/avoidInternship/type";
import { InternshipProgramApi } from "@/api";
import { ModeEnum } from "@/api/enums/value";
import { useLoading } from "@/hooks";
import useApplicationStore from "@/store/modules/application";
import { Toast } from "@/utils";

const { currentPlanInfo } = storeToRefs(useApplicationStore());
const { showLoading, hideLoading } = useLoading();

const freeInternshipId = ref<number>(0);
const mode = ref<ModeEnum>(ModeEnum.ADD);
onLoad((options) => {
  freeInternshipId.value = Number(options?.freeInternshipId);
  mode.value = options?.mode;
  console.log("onLoad===", options);
});

onMounted(() => {
  checkCanApplyFreeInternship();
  isEdit.value && getApplicationFreeDetail();
});

// 检查是否可以申请免实习
const disabledApply = ref<boolean>(true);
const checkCanApplyFreeInternship = async () => {
  try {
    await InternshipProgramApi.checkCanApplyFreeInternship({
      internshipPlanId: currentPlanInfo.value.id,
    });
    disabledApply.value = false;
  } catch (err) {
    console.log(err);
    disabledApply.value = false;
  }
};

// 获取免实习申请详情
const detailInfo = ref<GetApplicationFreeDetailRes>();
const getApplicationFreeDetail = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getApplicationFreeDetail({
      freeInternshipId: freeInternshipId.value,
    });
    detailInfo.value = data;
    formData.value = data;
    uploadImgData.value.attachment = JSON.parse(data.attachment || "");

    console.log("getApplicationDetail", data);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};
const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const internshipReasonoRef = ref();
const uploadImgRef = ref();
const formData = ref<IInternshipReason>({
  detail: "",
});
const uploadImgData = ref<IUploadImgForm>({
  attachment: [],
});
const onSubmit = async () => {
  const allInfo: any = {
    ...formData.value,
    attachment: JSON.stringify(uploadImgData.value.attachment),
  };
  console.log("All forms validated successfully", allInfo);
  try {
    // 提交表单数据
    let res;
    showLoading();
    if (isAdd.value) {
      res = await InternshipProgramApi.submitApplicationFree(allInfo, {
        internshipPlanId: currentPlanInfo.value.id,
      });
      Toast("申请成功");
    } else {
      res = await InternshipProgramApi.editFreeApplication(allInfo, {
        freeInternshipId: freeInternshipId.value,
      });
      Toast("修改成功");
    }
    uni.navigateBack();

    console.log("Form submitted successfully", res);
  } catch (error) {
    console.error("Error submitting form", error);
  } finally {
    hideLoading();
  }
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button {
  height: 88rpx !important;

  .u-button__text {
    font-size: 32rpx !important;
  }
}
</style>
