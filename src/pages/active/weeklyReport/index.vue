<template>
  <ContentBox
    :title="title"
    height="202rpx"
    :background="bgImage"
    :is-show-search="false"
    :show-header="true">
    <WeeklyReportApply
      v-if="currentPage === 'WeeklyReportApply'"
      :plan-id="currentPlanInfo.id"
      :student-weekly-report-id="studentWeeklyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
    <WeeklyReportApply
      v-else-if="currentPage === 'AfterApply'"
      :type="PageEnum.patch"
      :plan-id="currentPlanInfo.id"
      :student-weekly-report-id="studentWeeklyReportId" />
    <WeeklyReportRecord
      v-else-if="currentPage === 'WeeklyReportRecord'"
      ref="weeklyReportRecordRef"
      :plan-id="currentPlanInfo.id"
      :student-weekly-report-id="studentWeeklyReportId"
      :is-add="isAdd"
      :is-edit="isEdit" />
  </ContentBox>
  <TabBar v-model="currentPage" :tab-list="tabbarList" />
</template>

<script lang="ts" setup>
import { ModeEnum, PageEnum } from "@/api/enums/value";
import smallNavBarBg from "@/static/images/smallNavBarBg.png";
import useApplicationStore from "@/store/modules/application";
import { urlTobase64 } from "@/utils";
import { tabbarList } from "./common";

const { currentPlanInfo } = storeToRefs(useApplicationStore());

const bgImage = urlTobase64(smallNavBarBg);

const currentPage = ref<string>("WeeklyReportApply");
const mode = ref<ModeEnum>(ModeEnum.ADD);
const studentWeeklyReportId = ref<number>(0);
onLoad((options) => {
  console.log("接收到的参数:", options);
  currentPage.value = options?.type || "WeeklyReportApply";
  mode.value = options?.mode || ModeEnum.ADD;
  studentWeeklyReportId.value = options?.studentWeeklyReportId;
});

const isAdd = computed(() => mode.value === ModeEnum.ADD);
const isEdit = computed(() => mode.value === ModeEnum.EDIT);

const weeklyReportRecordRef = ref();
onShow(() => {
  weeklyReportRecordRef.value?.init();
});

const title = computed(() => {
  const allTitle = {
    WeeklyReportApply: "周报",
    AfterApply: "周报",
    WeeklyReportRecord: "周报记录",
  };
  return allTitle[currentPage.value];
});
</script>
