<template>
  <view class="flex flex-col gap-y-24rpx p-24rpx">
    <Instructor :data="teachInfo" />
    <DetailProgram :data="planInfo" />
    <ProcessManage :data="planInfo" />
    <AssessmentManage :data="planInfo" />
    <view class="mt-24rpx flex flex-col gap-y-32rpx">
      <up-button
        :custom-style="{
          height: '88rpx',
        }"
        text="实习申请"
        shape="circle"
        color="linear-gradient( 272deg, #D63C38 3%, #E76B67 100%)"
        :disabled="!canApply"
        @click="goApplication"></up-button>
      <up-button
        :custom-style="{
          border: '1px solid #D63C38',
          color: '#D63C38',
          height: '88rpx',
        }"
        text="免实习申请"
        color="#FFE7E6"
        shape="circle"
        :disabled="!canApply"
        @click="goAvoidInternship" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { Plan, Teacher } from "@/api/internshipProgram/types";
import { InternshipProgramApi } from "@/api";
import { useLoading } from "@/hooks";
import { useApplicationStore } from "@/store";

const id = ref<number>(1);
onLoad((options) => {
  id.value = Number(options?.id);
  console.log("onLoad===", options);
});

onMounted(() => {
  getStudentInternshipDetail();
});

// 指导教师
const teachInfo = ref<Teacher>();
// 实习计划
const planInfo = ref<Plan>();
const canApply = ref<boolean>(false);

const { showLoading, hideLoading } = useLoading();
// 获取学生的实习计划列表
const getStudentInternshipDetail = async () => {
  try {
    showLoading();
    const data = await InternshipProgramApi.getStudentInternshipDetail({
      planId: id.value,
    });
    console.log("getData", data);
    teachInfo.value = data.teacher;
    planInfo.value = data.plan;
    canApply.value = data.can_apply;
    setCurrentPlanInfo(data.plan);
  } catch (err) {
    console.log(err);
  } finally {
    hideLoading();
  }
};

const { setCurrentPlanInfo } = useApplicationStore();

const goApplication = () => {
  uni.navigateTo({
    url: "/pages/active/internshipApplication/application",
  });
};

const goAvoidInternship = () => {
  uni.navigateTo({
    url: "/pages/active/avoidInternship/application",
  });
};
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: "shared",
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-button__text {
  font-size: 32rpx !important;
}
</style>
