interface WxConfig {
  debug: boolean;
  appId: string;
  timestamp: number;
  nonceStr: string;
  signature: string;
  jsApiList: string[];
}

interface Wx {
  config: (config: WxConfig) => void;
  ready: (callback: () => void) => void;
  error: (callback: (err: any) => void) => void;
  getLocation: (options: {
    type: "wgs84" | "gcj02";
    success: (res: WechatMiniprogram.GetLocationSuccessCallbackResult) => void;
    fail: (err: any) => void;
  }) => void;
  saveImageToPhotosAlbum: (options: {
    localId: string;
    success: (res: any) => void;
    fail: (err: any) => void;
  }) => void;
  uploadImage: (options: {
    localId: string;
    isShowProgressTips: number;
    success: (res: { serverId: string }) => void;
    fail: (err: any) => void;
  }) => void;
}

declare const wx: Wx;
