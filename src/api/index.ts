import * as Ask<PERSON>eave<PERSON><PERSON> from "./askLeave";
import * as Common<PERSON><PERSON> from "./common";
import * as CompanyTeacherApi from "./companyTeacher";
import * as DailyReportApi from "./dailyReport";
import * as EmploymentReportApi from "./employmentReport";
import * as Feed<PERSON><PERSON><PERSON> from "./feedback";
import * as Home<PERSON><PERSON> from "./home";
import * as InternshipProgramApi from "./internshipProgram";
import * as InternshipSummaryApi from "./internshipSummary";
import * as Jianli<PERSON><PERSON> from "./jianli";
import * as Message<PERSON><PERSON> from "./message";
import * as MonthlyReportApi from "./monthlyReport";
import * as MyGradesApi from "./myGrades";
import * as PaySlipApi from "./paySlip";
import * as SignInApi from "./signIn";
import * as SurveyPaperApi from "./surveyPaper";
import * as UserApi from "./user";
import * as VisaFreeApi from "./visaFree";
import * as WeeklyReportApi from "./weeklyReport";

export {
  AskLeaveApi, // 请假相关接口
  CommonApi, // 公共接口
  CompanyTeacherApi,
  DailyReportApi, // 日报相关接口
  EmploymentReportApi, // 就业上报相关接口
  FeedbackApi, // 反馈相关接口
  HomeApi, // 首页相关接口
  InternshipProgramApi, // 实习计划相关接口
  InternshipSummaryApi, // 实习总结相关接口
  JianliApi,
  MessageApi, // 消息相关接口
  MonthlyReportApi, // 月报相关接口
  MyGradesApi, // 我的成绩相关接口
  PaySlipApi, // 我的工资相关接口
  SignInApi, // 签到相关接口
  SurveyPaperApi, // 学生问卷相关接口
  UserApi, // 用户相关接口
  VisaFreeApi, // 免签相关接口
  WeeklyReportApi, // 周报相关接口
};
