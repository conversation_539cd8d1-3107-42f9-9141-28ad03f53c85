/**
 * 用户信息相关接口
 */
import type { CommonRes } from "@/api/common/types";
import type {
  LoginByCodeReq,
  LoginByCodeRes,
  LoginReq,
  LoginRes,
  ProfileReq,
  ProfileRes,
} from "./types";
import { apiPrefix } from "@/config";
import { get, post } from "@/utils/request";

/** 获取用户信息 */
export const profile = (params?: ProfileReq) =>
  get<ProfileRes>(`${apiPrefix}/personal/info`, { params });

/** 编辑用户信息 */
export const uerInfoEdit = (data) =>
  post<CommonRes>(`${apiPrefix}/personal/info/edit`, { data });

/** 登录 */
export const login = (data: LoginReq) =>
  post<LoginRes>(`${apiPrefix}/login`, { data, isToken: false });

/** 验证码登录 */
export const loginByCode = (data: LoginByCodeReq) =>
  post<LoginByCodeRes>(`${apiPrefix}/code_login`, { data });

/** 退出登录 */
export const logout = () => post<CommonRes>("/user/logout");
