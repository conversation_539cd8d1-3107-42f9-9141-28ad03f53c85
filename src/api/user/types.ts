export interface ProfileReq {
  user_id?: string;
}

export interface ProfileRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

export interface LoginReq {
  user_code: string;
  password: string;
}

export interface LoginRes {
  token: string;
  user_id: number;
  student_name: string;
  avatar: string;
}

export interface LoginByCodeReq {
  code?: string;
  user_code?: string;
  student_unique_client_id?: string;
}

export interface LoginByCodeRes {
  [key: string]: any;
}
