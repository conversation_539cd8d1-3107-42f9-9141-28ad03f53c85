/**
 * 通用接口
 */
import type {
  GetWxJsSdkConfigReq,
  GetWxJsSdkConfigRes,
  SendCodeReq,
  SendCodeRes,
  UploadRes,
} from "./types";
import { apiPrefix } from "@/config";
import { get, post, upload } from "@/utils/request";

// 文件上传
export const uploadFile = (filePath: string) =>
  upload<UploadRes>(`${apiPrefix}/file/upload`, { filePath, name: "file" });

// 发送验证码
export const sendCode = (data: SendCodeReq) =>
  post<SendCodeRes>("/sendCode", { data });

/** 获取微信jssdk配置 */
export const getWxJsSdkConfig = (params?: GetWxJsSdkConfigReq) =>
  get<GetWxJsSdkConfigRes>(`${apiPrefix}/wx_js_sdk_config`, {
    params,
  });
