/*
  字典值集合，仅收集前端需要用到的字典值
 */

export enum ModeEnum {
  ADD = "ADD", // 新增
  EDIT = "EDIT", // 编辑
  DETAIL = "DETAIL", // 详情
}

// pending，approved，rejected, revoked
export enum StatusEnum {
  pending = "pending", // 申请中
  approved = "approved", // 已通过
  rejected = "rejected", // 已驳回
  revoked = "revoked", // 已撤销
  SUBMITTED = "SUBMITTED", // 已提交
  END = "END", // 已结束
  REPORT = "REPORT", // 已上报
  REVIEWED = "REVIEWED", // 已批阅
  UNDERREVIEW = "UNDERREVIEW", // 待批阅
}
// # pending，待批阅，confirmed，已批阅，rejected，驳回
export enum ReadOverEnum {
  pending = "pending", // 待批阅
  confirmed = "approved", // 已批阅
  rejected = "rejected", // 已驳回
  revoked = "revoked", // 已撤销
}
// comfirmed
export enum FeedbackEnum {
  revoked = "revoked", // 已撤销
  confirmed = "confirmed", // 已提交
}

// 实习计划状态
export enum PlanEnum {
  pending = "pending", // 待开始
  in_progress = "in_progress", // 进行中
  finished = "finished", // 已结束
}

// 申请状态
export enum ApplyEnum {
  audit = "audit", // 审核
  readOver = "readOver", // 批阅
  feedback = "feedback", // 反馈
}

// 页面状态
export enum PageEnum {
  patch = "patch", // 补交
}

// 打卡状态 checked , un_check, abnormal, free, leave, address_abnormal
export enum CheckEnum {
  abnormal = "abnormal", // 异常
  checked = "checked", // 已打卡
  un_check = "un_check", // 未打卡
  free = "free", // 免签
  leave = "leave", // 请假
  address_abnormal = "address_abnormal", // 地址异常
}

export enum QuestionnaireEnum {
  finished = "finished", // 已完成
  notFinished = "not_finished", // 未完成
}

// 题型 单选题Single-Choice，判断题Judgment, 主观题 Subjective"
export enum QuestionTypeEnum {
  SingleChoice = "Single-Choice",
  Judgment = "Judgment",
  Subjective = "Subjective",
}

// 消息通知跳转枚举
export enum MessageEnum {
  notify_announcement = "notify_announcement",
  internship_before_notify_student_apply_for_internship = "internship_before_notify_student_apply_for_internship",
  student_unsign_warning = "student_unsign_warning",
  student_address_abnormal_warning = "student_address_abnormal_warning",
  reminder_teacher_guide = "reminder_teacher_guide",
  attendance = "attendance",
  attendance_abnormal = "attendance_abnormal",
  daily_report = "daily_report",
  monthly_report = "monthly_report",
  self_evaluation = "self_evaluation",
  contact_teacher = "contact_teacher",
  survey = "survey",
  summary_report = "summary_report",
  weekly_report = "weekly_report",
}

// 文件操作
export enum FileActionEnum {
  preview = "preview",
  delete = "delete",
}
