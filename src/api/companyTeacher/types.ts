import type { Indicators } from "../myGrades/types";

/** 登录 */
export interface LoginReq {
  phone_number?: string;
  student_id?: number;
  internship_plan_id?: number;
}

export interface LoginRes {
  token?: string;
}

/** 获取企业考核评分 */
export interface GetScoreDetailReq {}

export interface GetScoreDetailRes {
  evaluated: boolean;
  indicators: Indicators[];
  internship_assessment: any;
  internship_plan: any;
  teacher_name: string;
  teacher_phone: string;
}

/** 评分 */
export interface SubmitScoreReq {
  indicator_name?: string;
  indicator_id?: number;
  indicator_score?: number;
  score?: number;
}

export interface SubmitScoreRes {}
