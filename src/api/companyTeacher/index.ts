/**
 * 企业教师相关接口
 */
import type {
  GetScoreDetailReq,
  GetScoreDetailRes,
  LoginReq,
  LoginRes,
  SubmitScoreReq,
  SubmitScoreRes,
} from "./types";
import { apiCompanyPrefix } from "@/config";
import { get, post } from "@/utils/request";

const { VITE_API_COMPANY_PREFIX } = import.meta.env;

/** 登录 */
export const login = (data?: LoginReq) =>
  post<LoginRes>(`${apiCompanyPrefix}/login`, {
    baseURL: VITE_API_COMPANY_PREFIX,
    data,
  });

/** 获取企业考核评分 */
export const getScoreDetail = (params?: GetScoreDetailReq) =>
  get<GetScoreDetailRes>(`${apiCompanyPrefix}/student_score/get_score_detail`, {
    baseURL: VITE_API_COMPANY_PREFIX,
  });

/** 评分 */
export const submitScore = (data: SubmitScoreReq[]) =>
  post<SubmitScoreRes>(`${apiCompanyPrefix}/student_score/submit_score`, {
    baseURL: VITE_API_COMPANY_PREFIX,
    data,
  });
