/** 获取学生的实习计划列表 */
export interface StudentInternshipListRep {
  user_id?: string;
}

export interface StudentInternshipListRes {
  created_by?: string;
  id?: number;
  name?: string;
  plan_end_date?: string;
  plan_start_date?: string;
  status?: string;
  type?: string;
}

/** 获取学生的实习计划详情 */
export interface StudentInternshipDetailRep {
  planId?: number;
}

export interface StudentInternshipDetailRes {
  plan: Plan;
  teacher: Teacher;
  can_apply: boolean;
}

export interface Plan {
  code: string; // 实习计划编号
  content: string; // 实习计划内容
  weekly_report_percentage: number; // 周报要求百分比
  self_score_indicator_group_name: any; // 自评分指标组名称
  self_score_indicator_group_id: any; // 自评分指标组ID
  student_count: number; // 学生人数
  updated_at: string; // 更新时间
  type: string; // 实习计划类型
  requirement: string; // 实习要求
  monthly_report_percentage: number; // 月报要求百分比
  internal_teacher_score_percentage: number; // 内部教师评分百分比
  company_satisfaction_servey_name: any; // 公司满意度调查名称
  unified_arrange_count: number; // 统一安排人数
  method: string; // 实习方法
  daily_report_requirement: string; // 日报要求
  summary_percentage: number; // 总结要求百分比
  internal_teacher_score_indicator_group_name: any; // 内部教师评分指标组名称
  company_satisfaction_servey_id: any; // 公司满意度调查ID
  self_arrange_count: number; // 自行安排人数
  description: string; // 实习计划描述
  weekly_report_requirement: any; // 周报要求
  attendance_percentage: number; // 出勤要求百分比
  internal_teacher_score_indicator_group_id: any; // 内部教师评分指标组ID
  student_satisfaction_servey_name: any; // 学生满意度调查名称
  status: string; // 实习计划状态
  plan_start_date: string; // 实习计划开始日期
  monthly_report_requirement: any; // 月报要求
  before_internship_assessment_percentage: number; // 实习前评估百分比
  company_teacher_score_percentage: number; // 公司教师评分百分比
  student_satisfaction_servey_id: any; // 学生满意度调查ID
  deleted: boolean; // 是否删除
  plan_end_date: string; // 实习计划结束日期
  summary_requirement: any; // 总结要求
  after_internship_assessment_servey_name: any; // 实习后评估调查名称
  company_teacher_score_indicator_group_name: any; // 公司教师评分指标组名称
  attachment: string; // 附件
  created_by: string; // 创建人
  name: string; // 实习计划名称
  purpose: string; // 实习目的
  attendance: any; // 出勤情况
  after_internship_assessment_servey_id: any; // 实习后评估调查ID
  company_teacher_score_indicator_group_id: any; // 公司教师评分指标组ID
  insurance_certificate: string; // 保险证明
  actual_end_date: any; // 实际结束日期
  id: number; // 实习计划ID
  daily_report_percentage: number; // 日报要求百分比
  after_internship_assessment_percentage: number; // 实习后评估百分比
  self_score_percentage: number; // 自评分百分比
  other_attachment: any; // 其他附件
  created_at: string; // 创建时间
}

export interface Teacher {
  avatar: any;
  name: string;
}

/** 获取学生的申请列表 */
export interface StudentApplicationListRep {
  internshipPlanId?: number;
}

export interface StudentApplicationListRes {
  applications?: StudentApplicationList[];
  can_apply?: boolean;
}

export interface StudentApplicationList {
  company_id?: number; // 公司ID
  company_name?: string; // 公司名称
  id?: number; // 申请ID
  position_id?: number; // 岗位ID
  created_at?: string; // 创建时间
  internship_plan_id?: number; // 实习计划ID
  position_name?: string; // 岗位名称
  expected_start_date?: string; // 预计开始日期
  audit_status?: string; // 审核状态
  expected_end_date?: string; // 预计结束日期
}

/** 创建实习计划申请 */
export interface StartStudentApplicationRep {
  matchLabel?: string;
  internship_plan_id?: number; // 实习计划ID
  company_id?: number; // 公司ID
  position_id?: number; // 岗位ID
  expected_start_date?: string; // 预计开始日期
  expected_end_date?: string; // 预计结束日期
  other_attachment?: string; // 其他附件
  insurance_attachment?: string; // 保险附件
  agreement_attachment?: string; // 协议附件
  is_match?: boolean; // 是否匹配
  work_time?: string; // 工作时间
  off_time?: string; // 休息时间
  position_salary?: number; // 岗位薪资
  internship_content?: string; // 实习内容
  company_teacher_name?: string; // 公司导师姓名
  company_teacher_phone_number?: string; // 公司导师电话号码
}

export interface StartStudentApplicationQueryRep {
  internshipPlanId?: number;
}

export interface StartStudentApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 编辑实习计划申请 */
export interface EditStudentApplicationRep {
  internshipPlanId?: number;
  applicationId?: number;
}

export interface EditStudentApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 撤销实习计划申请 */
export interface CancelStudentApplicationRep {
  internshipPlanId?: number;
  applicationId?: number;
}

export interface CancelStudentApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取实习计划公司列表 */
export interface GetCompanyListRep {
  internshipPlanId?: number;
}

export interface GetCompanyListRes {
  company_id?: number;
  company_name?: string;
}

/** 获取实习计划公司岗位列表 */
export interface GetPositionListRep {
  internshipPlanId?: number;
  companyId?: number;
}

export interface GetPositionListRes {
  position_id?: number;
  position_name?: string;
}

/** 获取公司详情 */
export interface GetCompanyDetailRep {
  companyId?: number;
}

export interface GetCompanyDetailRes {
  name?: string; // 公司名称
  business_license_photo?: string; // 营业执照照片
  audit_time?: string; // 审核时间
  nature?: string; // 公司性质
  is_long_term_operate?: boolean; // 是否长期运营
  audit_by?: any; // 审核人
  principal?: string; // 公司负责人
  business_license_validity?: any; // 营业执照有效期
  audit_remark?: any; // 审核备注
  address?: string; // 公司地址
  industry_category?: string; // 行业类别
  created_at?: string; // 创建时间
  agreement_start_time?: string; // 协议开始时间
  created_by?: string; // 创建人
  updated_at?: string; // 更新时间
  code?: string; // 公司代码
  agreement_end_time?: string; // 协议结束时间
  phone_number?: string; // 公司电话号码
  is_expired?: boolean; // 是否过期
  agreement_attachment?: string; // 协议附件
  id?: number; // 公司ID
  credit_code?: string; // 统一社会信用代码
  audit_status?: string; // 审核状态
}

/** 获取岗位详情 */
export interface GetPositionDetailRep {
  positionId?: number;
}

export interface GetPositionDetailRes {
  id?: number; // 岗位ID
  location_lng?: string; // 经度
  audit_by?: string; // 审核人
  company_id?: number; // 公司ID
  location_lat?: string; // 纬度
  education?: string; // 教育要求
  major_type?: string; // 专业类型
  company_name?: string; // 公司名称
  nature?: string; // 公司性质
  industry?: string; // 行业
  description?: string; // 岗位描述
  reject_audit_remark?: any; // 拒绝审核备注
  name?: string; // 岗位名称
  min_salary?: string; // 最低薪资
  created_by?: string; // 创建人
  department?: string; // 部门
  max_salary?: string; // 最高薪资
  created_at?: string; // 创建时间
  code?: string; // 岗位代码
  location?: string; // 地址
  audit_status?: string; // 审核状态
  updated_at?: string; // 更新时间
  audit_time?: string; // 审核时间
}

/** 获取岗位详情 */
export interface GetApplicationDetailRep {
  applicationId?: number;
}

export interface GetApplicationDetailRes {
  company?: GetCompanyDetailRes;
  position?: GetPositionDetailRes;
  application?: IApplication;
}

export interface IApplication {
  matchLabel?: string;
  department_id?: number; // 部门ID
  student_id?: number; // 学生ID
  expected_start_date?: string; // 预计开始日期
  audit_status?: string; // 审核状态
  work_time?: string; // 工作时间
  department_name?: string; // 部门名称
  student_name?: string; // 学生姓名
  expected_end_date?: string; // 预计结束日期
  teacher_name?: string; // 导师姓名
  off_time?: string; // 休息时间
  major_id?: number; // 专业ID
  student_code?: string; // 学生代码
  internship_content?: string; // 实习内容
  teacher_id?: number; // 导师ID
  finished?: boolean; // 是否完成
  major_name?: string; // 专业名称
  company_id?: number; // 公司ID
  is_match?: boolean; // 是否匹配
  company_teacher_name?: string; // 公司导师姓名
  show?: boolean; // 是否显示
  number?: string; // 编号
  class_id?: number; // 班级ID
  company_name?: string; // 公司名称
  agreement_attachment?: any; // 协议附件
  company_teacher_phone_number?: string; // 公司导师电话号码
  is_last?: boolean; // 是否最后一个
  id?: number; // 申请ID
  class_name?: string; // 班级名称
  position_id?: number; // 岗位ID
  audit_time?: any; // 审核时间
  audit_remark?: any; // 审核备注
  created_at?: string; // 创建时间
  internship_plan_id?: number; // 实习计划ID
  grade_id?: number; // 年级ID
  position_name?: string; // 岗位名称
  insurance_attachment?: any; // 保险附件
  before_application_id?: any; // 前一个申请ID
  updated_at?: string; // 更新时间
  internship_plan_name?: string; // 实习计划名称
  grade_name?: string; // 年级名称
  position_salary?: number; // 岗位薪资
  other_attachment?: any; // 其他附件
  actual_end_date?: any; // 实际结束日期
}

/** 获取学生实习进度 */
export interface GetStudentProgressRep {
  internshipPlanId?: number;
}

export interface GetStudentProgressRes {
  student_phone: any; // 学生电话
  class_id: number; // 班级ID
  makeup_attendance_number: string; // 补签到次数
  total_monthly_report_number: string; // 月报总数
  year: number; // 年份
  internship_plan_id: number; // 实习计划ID
  student_name: string; // 学生姓名
  class_name: string; // 班级名称
  total_daily_report_number: string; // 日报总数
  monthly_report_number: string; // 已提交月报数
  internship_grade: string; // 实习成绩
  id: number; // ID
  grade_id: string; // 年级ID
  company_teacher_name: string; // 企业导师姓名
  un_monthly_report_number: string; // 未提交月报数
  total_summary_number: string; // 总结报告总数
  created_at: string; // 创建时间
  internship_plan_name: string; // 实习计划名称
  grade_name: string; // 年级名称
  total_attendance_number: string; // 考勤总数
  daily_report_number: string; // 已提交日报数
  summary_number: string; // 已提交总结数
  updated_at: string; // 更新时间
  teacher_id: number; // 指导教师ID
  department_id: number; // 部门ID
  attendance_number: string; // 已考勤次数
  un_daily_report_number: string; // 未提交日报数
  un_summary_number: string; // 未提交总结数
  teacher_name: string; // 指导教师姓名
  department_name: string; // 部门名称
  un_attendance_number: string; // 未考勤次数
  total_weekly_report_number: string; // 周报总数
  unit: string; // 单位
  student_id: number; // 学生ID
  major_id: number; // 专业ID
  leave_days: string; // 请假天数
  weekly_report_number: string; // 已提交周报数
  unit_number: number; // 单位数量
  student_code: string; // 学生学号
  major_name: string; // 专业名称
  free_attendance_number: string; // 免考勤次数
  un_weekly_report_number: string; // 未提交周报数
}

/** 获取实习计划申请批流程 */
export interface GetPlanApprovalFlowRep {
  applicationId?: number;
}

export interface GetPlanApprovalFlowRes {
  name?: string;
  time?: any;
  status?: string;
  audit_remark?: any;
}

/** 创建实习计划申请变更 */
export interface GetChangeApplicationListRep {
  internshipPlanId?: number;
}

export interface GetChangeApplicationListRes {
  applications?: ChangeApplicationList[];
  can_apply?: boolean;
}

export interface ChangeApplicationList {
  before_internship_application_id?: number; // 实习前申请ID
  change_after_position?: string; // 变更后的岗位名称
  change_before_position?: string; // 变更前的岗位名称
  change_before_company_name?: string; // 更前的公司名称
  after_internship_application_id?: number; // 实习后申请ID
  change_after_position_id?: number; // 变更后的岗位ID
  audit_status?: string; // 审核状态
  id?: number; // 变更申请ID
  created_at?: string; // 创建时间
  before_internship_actual_end_date?: string; // 实习前实际结束日期
  internship_plan_id?: number; // 实习计划ID
  change_after_company_name?: string; // 变更后的公司名称
}
/** 创建实习计划申请变更 */
export interface StartChangeApplicationRep {
  before_internship_actual_end_date?: string; // 实习前实际结束日期
  internship_plan_id?: number; // 实习计划ID
  company_id?: number; // 公司ID
  position_id?: number; // 岗位ID
  expected_start_date?: string; // 预计开始日期
  expected_end_date?: string; // 预计结束日期
  other_attachment?: string[]; // 其他附件
  insurance_attachment?: string[]; // 保险附件
  agreement_attachment?: string[]; // 协议附件
  is_match?: boolean; // 是否匹配
  work_time?: string; // 工作时间
  off_time?: string; // 休息时间
  position_salary?: string; // 岗位薪资
  internship_content?: string; // 实习内容
  company_teacher_name?: string; // 公司导师姓名
  company_teacher_phone_number?: string; // 公司导师电话号码
}
export interface StartChangeApplicationQuery {
  internshipPlanId?: number;
}

export interface StartChangeApplicationRes {
  before_internship_actual_end_date?: string; // 实习前实际结束日期
  internship_plan_id?: number; // 实习计划ID
  company_id?: number; // 公司ID
  position_id?: number; // 岗位ID
  expected_start_date?: string; // 预计开始日期
  expected_end_date?: string; // 预计结束日期
  other_attachment?: string[]; // 其他附件
  insurance_attachment?: string[]; // 保险附件
  agreement_attachment?: string[]; // 协议附件
  is_match?: boolean; // 是否匹配
  work_time?: string; // 工作时间
  off_time?: string; // 休息时间
  position_salary?: string; // 岗位薪资
  internship_content?: string; // 实习内容
  company_teacher_name?: string; // 公司导师姓名
  company_teacher_phone_number?: string; // 公司导师电话号码
}

/** 编辑实习计划变更申请 */
export interface EditApplicationChangeRep {
  before_internship_actual_end_date?: string; // 实习前实际结束日期
  internship_plan_id?: number; // 实习计划ID
  company_id?: number; // 公司ID
  position_id?: number; // 岗位ID
  expected_start_date?: string; // 预计开始日期
  expected_end_date?: string; // 预计结束日期
  other_attachment?: string[]; // 其他附件
  insurance_attachment?: string[]; // 保险附件
  agreement_attachment?: string[]; // 协议附件
  is_match?: boolean; // 是否匹配
  work_time?: string; // 工作时间
  off_time?: string; // 休息时间
  position_salary?: string; // 岗位薪资
  internship_content?: string; // 实习内容
  company_teacher_name?: string; // 公司导师姓名
  company_teacher_phone_number?: string; // 公司导师电话号码
}
export interface EditApplicationChangeQuery {
  changeApplicationId?: number;
}

export interface EditApplicationChangeRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 撤销实习计划变更申请 */
export interface CancelChangeApplicationRep {
  changeApplicationId?: number;
}

export interface CancelChangeApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取实习计划变更申请详情 */
export interface GetChangeApplicationRep {
  changeApplicationId?: number;
}

export interface GetChangeApplicationRes {
  company_change_info?: ChangeInfoItem[];
  position_change_info?: ChangeInfoItem[];
  internship_change_info?: ChangeInfoItem[];
  after_application?: AfterApplication;
  change_application?: ChangeApplication;
}

export interface ChangeInfoItem {
  name?: string;
  before: string & boolean;
  after: string & boolean;
}

export interface AfterApplication {
  code?: string; // 编号
  class_name?: string; // 班级名称
  position_id?: number; // 岗位ID
  audit_time?: any; // 审核时间
  audit_remark?: any; // 审核备注
  created_at?: string; // 创建时间
  internship_plan_id?: number; // 实习计划ID
  grade_id?: number; // 年级ID
  position_name?: string; // 岗位名称
  insurance_attachment?: string; // 保险附件
  before_application_id?: number; // 前一个申请ID
  updated_at?: string; // 更新时间
  internship_plan_name?: string; // 实习计划名称
  grade_name?: string; // 年级名称
  position_salary?: number; // 岗位薪资
  other_attachment?: string; // 其他附件
  actual_end_date?: any; // 实际结束日期
  id?: number; // ID
  department_id?: number; // 部门ID
  student_id?: number; // 学生ID
  expected_start_date?: string; // 预计开始日期
  audit_status?: string; // 审核状态
  work_time?: string; // 工作时间
  department_name?: string; // 部门名称
  student_name?: string; // 学生姓名
  expected_end_date?: string; // 预计结束日期
  teacher_name?: string; // 教师姓名
  off_time?: string; // 休息时间
  major_id?: number; // 专业ID
  student_code?: string; // 学生编号
  internship_content?: string; // 实习内容
  teacher_id?: number; // 教师ID
  finished?: boolean; // 是否完成
  major_name?: string; // 专业名称
  company_id?: number; // 公司ID
  is_match?: boolean; // 是否匹配
  company_teacher_name?: string; // 公司导师姓名
  show?: boolean; // 是否显示
  class_id?: number; // 班级ID
  company_name?: string; // 公司名称
  agreement_attachment?: string; // 协议附件
  company_teacher_phone_number?: string; // 公司导师电话号码
  is_last?: boolean; // 是否最后一个
}

export interface ChangeApplication {
  internship_plan_id?: number;
  student_phone?: any;
  major_name?: string;
  change_after_company_id?: number;
  internship_plan_name?: string;
  teacher_id?: number;
  class_id?: number;
  change_after_company_name?: string;
  before_internship_application_id?: number;
  teacher_name?: string;
  class_name?: string;
  change_after_position?: string;
  after_internship_application_id?: number;
  grade_id?: string;
  change_before_company_id?: number;
  change_after_position_id?: number;
  student_id?: number;
  grade_name?: string;
  change_before_company_name?: string;
  audit_status?: string;
  code?: string;
  student_code?: string;
  department_id?: number;
  change_before_position?: string;
  audit_remark?: any;
  id?: number;
  student_name?: string;
  department_name?: string;
  change_before_position_id?: number;
  created_at?: string;
  before_internship_actual_end_date?: string;
  major_id?: number;
  updated_at?: string;
}

/** 获取实习计划变更申请审批流程 */
export interface GetApprovalFlowRep {
  changeApplicationId?: number;
}

export interface GetApprovalFlowRes {
  name?: string;
  time?: any;
  status?: string;
  audit_remark?: any;
}

/** 检查是否可以申请免实习 */
export interface CheckCanApplyFreeInternshipRep {
  internshipPlanId?: number;
}

export interface CheckCanApplyFreeInternshipRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取免实习申请列表 */
export interface GetApplicationFreeListRep {
  internshipPlanId?: number;
}

export interface GetApplicationFreeListRes {
  applications?: GetApplicationFreeList[];
  can_apply?: boolean;
}

export interface GetApplicationFreeList {
  department_id?: number; // 部门ID
  grade_name?: string; // 年级名称
  created_at?: string; // 创建时间
  department_name?: string; // 部门名称
  student_id?: number; // 学生ID
  updated_at?: string; // 更新时间
  internship_plan_id?: number; // 实习计划ID
  major_id?: number; // 专业ID
  student_name?: string; // 学生姓名
  id?: number; // 申请ID
  major_name?: string; // 专业名称
  student_code?: string; // 学生代码
  internship_plan_name?: string; // 实习计划名称
  class_id?: number; // 班级ID
  detail?: string; // 详情
  code?: string; // 编号
  class_name?: string; // 班级名称
  audit_remark?: any; // 审核备注
  teacher_id?: number; // 导师ID
  grade_id?: number; // 年级ID
  attachment?: string; // 附件
  teacher_name?: string; // 导师姓名
  audit_status?: string; // 审核状态
}

/** 提交免实习申请 */
export interface SubmitApplicationFreeRep {
  internshipPlanId?: number;
}

export interface SubmitApplicationFreeQuery {
  internshipPlanId?: number;
}

export interface SubmitApplicationFreeRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}
/** 提交免实习申请 */
export interface EditFreeApplicationRep {
  internshipPlanId?: number;
}
export interface EditFreeApplicationQuery {
  freeInternshipId?: number;
}

export interface EditFreeApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}
/** 提交免实习申请 */
export interface RevokeFreeApplicationRep {
  freeInternshipId?: number;
}

export interface RevokeFreeApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取免实习申请详情 */
export interface GetApplicationFreeDetailRep {
  freeInternshipId?: number;
}

export interface GetApplicationFreeDetailRes {
  department_id?: number; // 部门ID
  grade_name?: string; // 年级名称
  created_at?: string; // 创建时间
  department_name?: string; // 部门名称
  student_id?: number; // 学生ID
  updated_at?: string; // 更新时间
  internship_plan_id?: number; // 实习计划ID
  major_id?: number; // 专业ID
  student_name?: string; // 学生姓名
  id?: number; // 申请ID
  major_name?: string; // 专业名称
  student_code?: string; // 学生代码
  internship_plan_name?: string; // 实习计划名称
  class_id?: number; // 班级ID
  detail?: string; // 详情
  code?: string; // 编号
  class_name?: string; // 班级名称
  audit_remark?: any; // 审核备注
  teacher_id?: number; // 导师ID
  grade_id?: number; // 年级ID
  attachment?: string; // 附件
  teacher_name?: string; // 导师姓名
  audit_status?: string; // 审核状态
}

/** 获取免实习申请审批流程 */
export interface GetFreeApprovalFlowRep {
  freeInternshipId?: number;
}

export interface GetFreeApprovalFlowRes {
  name?: string;
  time?: any;
  status?: string;
  audit_remark?: any;
}

/** 检查学生是否可以结束实习计划申请 */
export interface CheckCanEndApplicationRep {
  internshipPlanId?: number;
}

export interface CheckCanEndApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取学生的实习计划申请列表 */
export interface GetApplicationEndListRep {
  internshipPlanId?: number;
}

export interface GetApplicationEndListRes {
  can_apply?: boolean;
  application?: ApplicationEndList[];
}

export interface ApplicationEndList {
  audit_status?: string;
  company_id?: number;
  company_name?: string;
  expected_end_date?: string;
  expected_start_date?: string;
  id?: number;
  internship_plan_id?: number;
  position_id?: number;
  position_name?: string;
}

/** 获取学生的实习计划申请列表 */
export interface CreateEndInternshipApplicationRep {
  internshipPlanId?: number;
}

export interface CreateEndInternshipApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 撤销实习计划申请结束申请 */
export interface RevokeEndApplicationRep {
  endApplicationId?: number;
}

export interface RevokeEndApplicationRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取实习计划申请结束申请详情 */
export interface GetEndInternshipApplicationDetailRep {
  endApplicationId?: number;
}

export interface GetEndInternshipApplicationDetailRes {
  company_info?: any;
  position_info?: any;
  end_application_info: EndApplicationInfo;
  internship_application_info: InternshipApplicationInfo;
}

export interface EndApplicationInfo {
  student_name: string; // 学生姓名
  major_id: number; // 专业ID
  makeup_attendance_days: any; // 补签到天数
  monthly_report_total_count: number; // 月报总数
  code: string; // 编号
  student_phone: any; // 学生电话号码
  major_name: string; // 专业名称
  free_sign_days: any; // 免签天数
  monthly_report_unsubmitted_count: number; // 未提交的月报数量
  id: number; // 结束申请ID
  teacher_id: number; // 导师ID
  class_id: number; // 班级ID
  remark: any; // 备注信息
  associated_internship_application_id: number; // 关联的实习申请ID
  internship_plan_id: number; // 实习计划ID
  teacher_name: string; // 导师姓名
  class_name: string; // 班级名称
  leave_days: any; // 请假天数
  audit_status: string; // 审核状态
  internship_plan_name: string; // 实习计划名称
  grade_id: string; // 年级ID
  internship_plan_start_date: string; // 实习计划开始日期
  daily_report_total_count: number; // 日报总数
  audit_remark: any; // 审核备注
  actual_end_date: string; // 实际结束日期
  grade_name: string; // 年级名称
  internship_plan_end_date: string; // 实习计划结束日期
  daily_report_unsubmitted_count: number; // 未提交的日报数量
  created_at: string; // 创建时间
  student_id: number; // 学生ID
  department_id: number; // 部门ID
  required_attendance_days: any; // 要求出勤天数
  weekly_report_total_count: number; // 周报总数
  updated_at: string; // 更新时间
  student_code: string; // 学生代码
  department_name: string; // 部门名称
  actual_attendance_days: any; // 实际出勤天数
  weekly_report_unsubmitted_count: number; // 未提交的周报数量
}

export interface InternshipApplicationInfo {
  id: number; // 实习申请ID
  internship_plan_name: string; // 实习计划名称
  grade_name: string; // 年级名称
  position_salary: number; // 岗位薪资
  other_attachment: string; // 其他附件
  actual_end_date: any; // 实际结束日期（可能为空）
  department_id: number; // 所属部门ID
  student_id: number; // 学生ID
  expected_start_date: string; // 预计开始日期
  audit_status: string; // 审核状态
  work_time: string; // 工作时间
  department_name: string; // 所属部门名称
  student_name: string; // 学生姓名
  expected_end_date: string; // 预计结束日期
  teacher_name: string; // 导师姓名
  off_time: string; // 休息时间
  major_id: number; // 所学专业ID
  student_code: string; // 学生学号/代码
  internship_content: string; // 实习内容
  teacher_id: number; // 导师ID
  finished: boolean; // 是否已完成实习
  major_name: string; // 所学专业名称
  company_id: number; // 实习公司ID
  is_match: boolean; // 是否为匹配岗位
  company_teacher_name: string; // 公司导师姓名
  show: boolean; // 是否显示该申请（用于前端控制）
  code: string; // 实习申请编号
  class_id: number; // 所属班级ID
  company_name: string; // 实习公司名称
  agreement_attachment: string; // 协议附件路径
  company_teacher_phone_number: string; // 公司导师联系电话
  is_last: boolean; // 是否为最后一个申请
  internship_plan_id: number; // 关联的实习计划ID
  class_name: string; // 所属班级名称
  position_id: number; // 岗位ID
  audit_time: any; // 审核时间（可能为空）
  audit_remark: any; // 审核备注（可能为空）
  created_at: string; // 创建时间
  grade_id: number; // 年级ID
  position_name: string; // 岗位名称
  insurance_attachment: string; // 保险附件路径
  before_application_id: any; // 前一个申请ID（可能为空）
  updated_at: string; // 最后更新时间
}

/** 获取实习计划申请结束申请审批流程 */
export interface GetEndApprovalFlowRep {
  endApplicationId?: number;
}

export interface GetEndApprovalFlowRes {
  name?: string;
  time?: any;
  status?: string;
  audit_remark?: any;
}
