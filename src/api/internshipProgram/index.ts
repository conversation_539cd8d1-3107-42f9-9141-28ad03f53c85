/**
 * 实习计划相关接口
 */
import type {
  CancelChangeApplicationRep,
  CancelChangeApplicationRes,
  CancelStudentApplicationRep,
  CancelStudentApplicationRes,
  CheckCanApplyFreeInternshipRep,
  CheckCanApplyFreeInternshipRes,
  CheckCanEndApplicationRep,
  CheckCanEndApplicationRes,
  CreateEndInternshipApplicationRep,
  CreateEndInternshipApplicationRes,
  EditApplicationChangeQuery,
  EditApplicationChangeRep,
  EditApplicationChangeRes,
  EditFreeApplicationQuery,
  EditFreeApplicationRep,
  EditFreeApplicationRes,
  EditStudentApplicationRep,
  EditStudentApplicationRes,
  GetApplicationDetailRep,
  GetApplicationDetailRes,
  GetApplicationEndListRep,
  GetApplicationEndListRes,
  GetApplicationFreeDetailRep,
  GetApplicationFreeDetailRes,
  GetApplicationFreeListRep,
  GetApplicationFreeListRes,
  GetApprovalFlowRep,
  GetApprovalFlowRes,
  GetChangeApplicationListRep,
  GetChangeApplicationListRes,
  GetChangeApplicationRep,
  GetChangeApplicationRes,
  GetCompanyDetailRep,
  GetCompanyDetailRes,
  GetCompanyListRep,
  GetCompanyListRes,
  GetEndApprovalFlowRep,
  GetEndApprovalFlowRes,
  GetEndInternshipApplicationDetailRep,
  GetEndInternshipApplicationDetailRes,
  GetFreeApprovalFlowRep,
  GetFreeApprovalFlowRes,
  GetPlanApprovalFlowRep,
  GetPlanApprovalFlowRes,
  GetPositionDetailRep,
  GetPositionDetailRes,
  GetPositionListRep,
  GetPositionListRes,
  GetStudentProgressRep,
  GetStudentProgressRes,
  RevokeEndApplicationRep,
  RevokeEndApplicationRes,
  RevokeFreeApplicationRep,
  RevokeFreeApplicationRes,
  StartChangeApplicationQuery,
  StartChangeApplicationRep,
  StartChangeApplicationRes,
  StartStudentApplicationQueryRep,
  StartStudentApplicationRep,
  StartStudentApplicationRes,
  StudentApplicationListRep,
  StudentApplicationListRes,
  StudentInternshipDetailRep,
  StudentInternshipDetailRes,
  StudentInternshipListRes,
  SubmitApplicationFreeQuery,
  SubmitApplicationFreeRep,
  SubmitApplicationFreeRes,
} from "./types";
import { apiPrefix } from "@/config";
import { get, post } from "@/utils/request";

// ==============================实习计划============================
/** 获取学生的实习计划列表 */
export const getStudentInternshipList = () =>
  get<StudentInternshipListRes[]>(
    `${apiPrefix}/internship_plan/get_student_internship_list`,
  );

/** 获取学生的实习计划详情 */
export const getStudentInternshipDetail = (
  params?: StudentInternshipDetailRep,
) =>
  get<StudentInternshipDetailRes>(
    `${apiPrefix}/internship_plan/get_student_internship_detail/${params?.planId}`,
  );

// ==============================实习计划实习申请==========================
/** 获取学生的实习申请列表 */
export const studentApplicationList = (data?: StudentApplicationListRep) =>
  post<StudentApplicationListRes>(
    `${apiPrefix}/internship_plan_application/application/${data?.internshipPlanId}`,
  );

/** 检查学生是否可以申请实习计划 */
export const checkCanApply = (data?: StudentApplicationListRep) =>
  post<StudentApplicationListRes>(
    `${apiPrefix}/internship_plan_application/check_can_apply/${data?.internshipPlanId}`,
  );

/** 创建实习计划申请 */
export const startStudentApplication = (
  data?: StartStudentApplicationRep,
  query?: StartStudentApplicationQueryRep,
) =>
  post<StartStudentApplicationRes>(
    `${apiPrefix}/internship_plan_application/start_application/${query?.internshipPlanId}`,
    { data },
  );

/** 编辑实习计划申请 */
export const editStudentApplication = (
  data?: EditStudentApplicationRep,
  query?: EditStudentApplicationRep,
) =>
  post<EditStudentApplicationRes>(
    `${apiPrefix}/internship_plan_application/edit_application/${query?.internshipPlanId}/${query?.applicationId}`,
    { data },
  );

/** 撤销实习计划申请 */
export const revokeApplication = (data?: CancelStudentApplicationRep) =>
  post<CancelStudentApplicationRes>(
    `${apiPrefix}/internship_plan_application/revoke_application/${data?.internshipPlanId}/${data?.applicationId}`,
  );

/** 获取实习计划公司列表 */
export const getCompanyList = (data?: GetCompanyListRep) =>
  post<GetCompanyListRes[]>(
    `${apiPrefix}/internship_plan_application/get_company_list/${data?.internshipPlanId}`,
  );

/** 获取公司详情 */
export const getCompanyDetail = (params?: GetCompanyDetailRep) =>
  get<GetCompanyDetailRes>(
    `${apiPrefix}/internship_plan_application/get_company_detail/${params?.companyId}`,
  );

/** 获取实习计划公司岗位列表 */
export const getPositionList = (data?: GetPositionListRep) =>
  post<GetPositionListRes[]>(
    `${apiPrefix}/internship_plan_application/get_position_list/${data?.internshipPlanId}/${data?.companyId}`,
  );

/** 获取岗位详情 */
export const getPositionDetail = (params?: GetPositionDetailRep) =>
  get<GetPositionDetailRes>(
    `${apiPrefix}/internship_plan_application/get_position_detail/${params?.positionId}`,
  );

/** 获取实习申请详情 */
export const getApplicationDetail = (params?: GetApplicationDetailRep) =>
  get<GetApplicationDetailRes>(
    `${apiPrefix}/internship_plan_application/get_application_detail/${params?.applicationId}`,
  );

/** 获取实习计划申请批流程 */
export const getPlanApprovalFlow = (params?: GetPlanApprovalFlowRep) =>
  get<GetPlanApprovalFlowRes[]>(
    `${apiPrefix}/internship_plan_application/get_approval_flow/${params?.applicationId}`,
  );

/** 获取学生实习进度 */
export const getStudentProgress = (params?: GetStudentProgressRep) =>
  get<GetStudentProgressRes>(
    `${apiPrefix}/internship_plan_application/get_student_progress/${params?.internshipPlanId}`,
  );

// ==============================实习计划变更申请==========================
/** 获取实习计划变更申请列表 */
export const getChangeApplicationList = (data?: GetChangeApplicationListRep) =>
  post<GetChangeApplicationListRes>(
    `${apiPrefix}/internship_plan_application_change/application/${data?.internshipPlanId}`,
  );

/** 检查学生是否可以申请实习计划变更 */
export const checkCanApplyChangeApplication = (
  data?: StudentApplicationListRep,
) =>
  post<StudentApplicationListRes>(
    `${apiPrefix}/internship_plan_application_change/check_can_apply_change_application/${data?.internshipPlanId}`,
  );

/** 创建实习计划申请变更 */
export const startChangeApplication = (
  data?: StartChangeApplicationRep,
  query?: StartChangeApplicationQuery,
) =>
  post<StartChangeApplicationRes>(
    `${apiPrefix}/internship_plan_application_change/start_change_application/${query?.internshipPlanId}`,
    { data },
  );

/** 编辑实习计划变更申请 */
export const editApplicationChange = (
  data?: EditApplicationChangeRep,
  query?: EditApplicationChangeQuery,
) =>
  post<EditApplicationChangeRes>(
    `${apiPrefix}/internship_plan_application_change/edit_application_change/${query?.changeApplicationId}`,
    { data },
  );

/** 撤销实习计划变更申请 */
export const cancelChangeApplication = (data?: CancelChangeApplicationRep) =>
  post<CancelChangeApplicationRes>(
    `${apiPrefix}/internship_plan_application_change/revoke_change_application/${data?.changeApplicationId}`,
  );

/** 获取实习计划变更申请详情 */
export const getChangeApplication = (params?: GetChangeApplicationRep) =>
  get<GetChangeApplicationRes>(
    `${apiPrefix}/internship_plan_application_change/get_change_application/${params?.changeApplicationId}`,
  );

/** 获取实习计划变更申请审批流程 */
export const getApprovalFlow = (params?: GetApprovalFlowRep) =>
  get<GetApprovalFlowRes[]>(
    `${apiPrefix}/internship_plan_application_change/get_approval_flow/${params?.changeApplicationId}`,
  );

// ==============================实习计划免实习申请==========================
/** 检查是否可以申请免实习 */
export const checkCanApplyFreeInternship = (
  params?: CheckCanApplyFreeInternshipRep,
) =>
  get<CheckCanApplyFreeInternshipRes>(
    `${apiPrefix}/internship_plan_application_free/check_can_apply_free_internship/${params?.internshipPlanId}`,
  );

/** 获取免实习申请列表 */
export const getApplicationFreeList = (params?: GetApplicationFreeListRep) =>
  get<GetApplicationFreeListRes>(
    `${apiPrefix}/internship_plan_application_free/list/${params?.internshipPlanId}`,
  );

/** 提交免实习申请 */
export const submitApplicationFree = (
  data?: SubmitApplicationFreeRep,
  query?: SubmitApplicationFreeQuery,
) =>
  post<SubmitApplicationFreeRes>(
    `${apiPrefix}/internship_plan_application_free/submit/${query?.internshipPlanId}`,
    { data },
  );

/** 获取免实习申请详情 */
export const getApplicationFreeDetail = (
  params?: GetApplicationFreeDetailRep,
) =>
  get<GetApplicationFreeDetailRes>(
    `${apiPrefix}/internship_plan_application_free/detail/${params?.freeInternshipId}`,
  );

/** 编辑免实习申请 */
export const editFreeApplication = (
  data?: EditFreeApplicationRep,
  query?: EditFreeApplicationQuery,
) =>
  post<EditFreeApplicationRes>(
    `${apiPrefix}/internship_plan_application_free/edit_application/${query?.freeInternshipId}`,
    { data },
  );

/** 撤销免实习申请 */
export const revokeFreeApplication = (data?: RevokeFreeApplicationRep) =>
  post<RevokeFreeApplicationRes>(
    `${apiPrefix}/internship_plan_application_free/revoke_application/${data?.freeInternshipId}`,
  );

/** 获取免实习申请审批流程 */
export const getFreeApprovalFlow = (params?: GetFreeApprovalFlowRep) =>
  get<GetFreeApprovalFlowRes[]>(
    `${apiPrefix}/internship_plan_application_free/get_approval_flow/${params?.freeInternshipId}`,
  );

// ==============================实习计划申请结束==========================
/** 检查学生是否可以结束实习计划申请 */
export const checkCanEndApplication = (params?: CheckCanEndApplicationRep) =>
  get<CheckCanEndApplicationRes>(
    `${apiPrefix}/internship_plan_application_end/check_can_end_application/${params?.internshipPlanId}`,
  );

/** 获取学生的实习计划结束申请列表 */
export const getApplicationEndList = (data?: GetApplicationEndListRep) =>
  post<GetApplicationEndListRes>(
    `${apiPrefix}/internship_plan_application_end/application/${data?.internshipPlanId}`,
  );

/** 创建学生的实习计划结束申请 */
export const createEndInternshipApplication = (
  query?: CreateEndInternshipApplicationRep,
) =>
  post<CreateEndInternshipApplicationRes>(
    `${apiPrefix}/internship_plan_application_end/create_end_internship_application/${query?.internshipPlanId}`,
  );

/** 撤销实习计划申请结束申请 */
export const revokeEndApplication = (data?: RevokeEndApplicationRep) =>
  post<RevokeEndApplicationRes>(
    `${apiPrefix}/internship_plan_application_end/revoke_end_application/${data?.endApplicationId}`,
  );

/** 获取实习计划申请结束申请详情 */
export const getEndInternshipApplicationDetail = (
  params?: GetEndInternshipApplicationDetailRep,
) =>
  get<GetEndInternshipApplicationDetailRes>(
    `${apiPrefix}/internship_plan_application_end/get_end_internship_application_detail/${params?.endApplicationId}`,
  );

/** 获取实习计划申请结束申请审批流程 */
export const getEndApprovalFlow = (params?: GetEndApprovalFlowRep) =>
  get<GetEndApprovalFlowRes[]>(
    `${apiPrefix}/internship_plan_application_end/get_approval_flow/${params?.endApplicationId}`,
  );
