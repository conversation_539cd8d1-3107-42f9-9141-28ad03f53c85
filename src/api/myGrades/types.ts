/** 获取考核成绩 */
export interface GetScoreReq {
  internshipPlanId?: number;
}

export interface GetScoreRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}

/** 获取单项考核评分 */
export interface GetScoreDetailReq {
  internshipPlanId?: number;
  assessmentType?: string;
}

export interface GetScoreDetailRes {
  internship_plan: InternshipPlan;
  internship_assessment: InternshipAssessment;
  indicators: Indicators[];
  teacher_name: string;
  teacher_phone: string;
  assess_status: boolean;
  teacher_info: any;
}

export interface Indicators {
  indicator_score?: number; // 指标分数
  indicator_id?: number; // 指标ID
  indicator_name?: string; // 指标名称
  score?: number;
}

export interface InternshipAssessment {
  internshipTypeLabel?: string;
  student_phone: any; // 学生电话
  class_id: number; // 班级ID
  position_id: number; // 岗位ID
  daily_report_score: any; // 日报评分
  before_internship_assessment_percentage: any; // 实习前评估百分比
  internal_teacher_score_percentage: any; // 校内教师评分百分比
  company_teacher_score_score: any; // 企业导师评分分数
  updated_at: string; // 更新时间
  student_name: string; // 学生姓名
  class_name: string; // 班级名称
  position_name: string; // 岗位名称
  weekly_report_percentage: any; // 周报百分比
  before_internship_assessment_servey_name: any; // 实习前评估调查名称
  internal_teacher_score_indicator_group_name: any; // 校内教师评分指标组名称
  self_score_percentage: number; // 自评百分比
  grade_id: string; // 年级ID
  teacher_id: number; // 教师ID
  teacher_code: string; // 教师编号
  weekly_report_score: any; // 周报评分
  before_internship_assessment_servey_id: any; // 实习前评估调查ID
  internal_teacher_score_indicator_group_id: any; // 校内教师评分指标组ID
  self_score_indicator_group_name: string; // 自评指标组名称
  internship_plan_id: number; // 实习计划ID
  grade_name: string; // 年级名称
  teacher_name: string; // 教师姓名
  company_teacher_name: string; // 企业导师姓名
  monthly_report_percentage: any; // 月报百分比
  before_internship_assessment_score: any; // 实习前评估分数
  internal_teacher_score_score: any; // 校内教师评分分数
  self_score_indicator_group_id: number; // 自评指标组ID
  id: number; // 唯一标识ID
  department_id: number; // 院系ID
  internship_type: string; // 实习类型
  monthly_report_score: any; // 月报评分
  summary_percentage: any; // 总结百分比
  after_internship_assessment_servey_name: any; // 实习后评估调查名称
  company_teacher_score_percentage: any; // 企业导师评分百分比
  self_score_score: number; // 自评分数
  internship_plan_name: string; // 实习计划名称
  department_name: string; // 院系名称
  internship_method: string; // 实习方式
  score_time: any; // 评分时间
  summary_score: any; // 总结评分
  after_internship_assessment_servey_id: any; // 实习后评估调查ID
  company_teacher_score_indicator_group_name: any; // 企业导师评分指标组名称
  score_status: string; // 评分状态
  student_id: number; // 学生ID
  major_id: number; // 专业ID
  company_id: number; // 公司ID
  score: any; // 总分
  attendance_percentage: any; // 考勤百分比
  after_internship_assessment_percentage: any; // 实习后评估百分比
  company_teacher_score_indicator_group_id: any; // 企业导师评分指标组ID
  is_on_the_job: boolean; // 是否在职
  student_code: string; // 学生学号
  major_name: string; // 专业名称
  company_name: string; // 公司名称
  daily_report_percentage: any; // 日报百分比
  attendance_score: any; // 考勤评分
  after_internship_assessment_score: any; // 实习后评估分数
  created_at: string; // 创建时间
}

export interface TeacherInfo {
  teacher_name?: string;
  teacher_phone_number?: string;
  name?: string;
  password?: any;
  department_name?: string;
  nation?: any;
  email?: any;
  created_by?: string;
  deleted?: boolean;
  updated_at?: string;
  avatar?: any;
  id?: number;
  code?: string;
  department_id?: any;
  gender?: any;
  id_card?: any;
  phone_number?: any;
  role?: any;
  created_at?: string;
}

/** 获取学生自评信息 */
export interface GetSelfScoreReq {
  internshipPlanId?: number;
}

export interface GetSelfScoreRes {
  internship_plan: InternshipPlan;
  internship_assessment: any;
  indicators: Array<Indicators>;
}

export interface InternshipPlan {
  typeLabel?: string;
  class_name?: string;
  id: number; // 实习计划ID
  content: string; // 实习内容
  weekly_report_percentage: number; // 周报占比百分比
  company_teacher_score_indicator_group_id: any; // 企业导师评分指标组ID
  self_score_percentage: number; // 自评分数占比百分比
  attachment: string; // 附件
  created_by: string; // 创建者
  code: string; // 实习计划编码
  requirement: string; // 实习要求
  monthly_report_percentage: number; // 月报占比百分比
  after_internship_assessment_servey_id: any; // 实习后评估调查ID
  self_score_indicator_group_name: string; // 自评指标组名称
  insurance_certificate: string; // 保险证明
  actual_end_date: any; // 实际结束日期
  type: string; // 实习类型
  daily_report_requirement: string; // 日报要求
  summary_percentage: number; // 总结占比百分比
  after_internship_assessment_percentage: number; // 实习后评估占比百分比
  self_score_indicator_group_id: number; // 自评指标组ID
  other_attachment: any; // 其他附件
  created_at: string; // 创建时间
  method: string; // 实习方式
  weekly_report_requirement: any; // 周报要求
  attendance_percentage: number; // 考勤占比百分比
  internal_teacher_score_percentage: number; // 校内导师评分占比百分比
  company_satisfaction_servey_name: string; // 企业满意度调查名称
  student_count: number; // 学生数量
  updated_at: string; // 更新时间
  description: string; // 描述
  monthly_report_requirement: any; // 月报要求
  before_internship_assessment_percentage: number; // 实习前评估占比百分比
  internal_teacher_score_indicator_group_name: string; // 校内导师评分指标组名称
  company_satisfaction_servey_id: number; // 企业满意度调查ID
  unified_arrange_count: number; // 统一安排数量
  plan_start_date: string; // 计划开始日期
  summary_requirement: any; // 总结要求
  before_internship_assessment_servey_name: any; // 实习前评估调查名称
  internal_teacher_score_indicator_group_id: number; // 校内导师评分指标组ID
  student_satisfaction_servey_name: string; // 学生满意度调查名称
  self_arrange_count: number; // 自行安排数量
  plan_end_date: string; // 计划结束日期
  attendance: any; // 考勤
  before_internship_assessment_servey_id: any; // 实习前评估调查ID
  company_teacher_score_percentage: number; // 企业导师评分占比百分比
  student_satisfaction_servey_id: number; // 学生满意度调查ID
  status: string; // 状态
  name: string; // 实习计划名称
  purpose: string; // 实习目的
  daily_report_percentage: number; // 日报占比百分比
  after_internship_assessment_servey_name: any; // 实习后评估调查名称
  company_teacher_score_indicator_group_name: any; // 企业导师评分指标组名称
  deleted: boolean; // 是否已删除
}

export interface Indicators {
  indicator_score?: number; // 指标分数
  indicator_id?: number; // 指标ID
  indicator_name?: string; // 指标名称
  score?: number;
}

/** 学生提交自评 */
export interface SubmitSelfScoreReq {
  indicator_name?: string;
  indicator_id?: number;
  indicator_score?: number;
  score?: number;
}

export interface SubmitSelfScoreQuery {
  internshipPlanId?: number;
}

export interface SubmitSelfScoreRes {
  user_id?: string;
  user_name?: string;
  avatar?: string;
  token?: string;
}
