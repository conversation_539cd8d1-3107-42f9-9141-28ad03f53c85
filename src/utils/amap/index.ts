/**
 * 高德地图工具类
 * 提供基于高德地图的地理编码、逆地理编码等功能
 */
import AMapLoader from "@amap/amap-jsapi-loader";

// 高德地图 API 密钥，建议从环境变量或配置文件中获取
// 请替换为您自己的高德地图 API 密钥
// key 6b12b010c54be3d2f34e5729d7c33ee6
// secret 541e94aff03a1769be629849a89e929b
const AMAP_KEY = "6b12b010c54be3d2f34e5729d7c33ee6";
const SECURITY_JS_CODE = "541e94aff03a1769be629849a89e929b";
const AMAP_VERSION = "2.0";
const AMAP_PLUGINS = ["AMap.Geocoder", "AMap.DistrictSearch"];

// AMap 实例
let amapInstance: any = null;
// Geocoder 实例
let geocoderInstance: any = null;

/**
 * 初始化高德地图 API
 * @returns {Promise<any>} 返回 AMap 实例
 */
export const initAMap = async (): Promise<any> => {
  if (amapInstance) {
    return amapInstance;
  }

  try {
    window._AMapSecurityConfig = {
      securityJsCode: SECURITY_JS_CODE,
    };
    const AMap = await AMapLoader.load({
      key: AMAP_KEY,
      version: AMAP_VERSION,
      plugins: AMAP_PLUGINS,
    });

    amapInstance = AMap;
    console.log("高德地图 API 初始化成功");
    return AMap;
  } catch (error) {
    console.error("高德地图 API 初始化失败:", error);
    throw error;
  }
};

/**
 * 获取 Geocoder 实例
 * @returns {Promise<any>} 返回 Geocoder 实例
 */
export const getGeocoder = async (): Promise<any> => {
  if (geocoderInstance) {
    return geocoderInstance;
  }

  try {
    const AMap = await initAMap();
    geocoderInstance = new AMap.Geocoder({
      radius: 1000, // 搜索半径，单位：米
      extensions: "all", // 返回基本+附加地址信息
    });
    return geocoderInstance;
  } catch (error) {
    console.error("获取 Geocoder 实例失败:", error);
    throw error;
  }
};

/**
 * 根据经纬度获取位置信息（逆地理编码）
 * @param {number} longitude - 经度
 * @param {number} latitude - 纬度
 * @returns {Promise<any>} 返回位置信息
 */
export const getLocationByCoords = async (
  longitude: number,
  latitude: number,
): Promise<any> => {
  // 验证经纬度参数
  if (!longitude || !latitude) {
    console.error("经纬度参数无效:", { longitude, latitude });
    throw new Error("经纬度参数无效");
  }

  try {
    // 获取 Geocoder 实例
    const geocoder = await getGeocoder();

    return new Promise((resolve, reject) => {
      try {
        geocoder.getAddress(
          [longitude, latitude],
          (status: string, result: any) => {
            if (status === "complete" && result.info === "OK") {
              // 解析地址信息
              const addressComponent = result.regeocode.addressComponent || {};
              const formattedAddress = result.regeocode.formattedAddress || "";

              // 构建结构化的位置信息
              const locationInfo = {
                formattedAddress, // 完整地址
                province: addressComponent.province || "", // 省份
                city: addressComponent.city || "", // 城市
                district: addressComponent.district || "", // 区县
                township: addressComponent.township || "", // 乡镇
                street: addressComponent.street || "", // 街道
                streetNumber: addressComponent.streetNumber || "", // 门牌号
                neighborhood: addressComponent.neighborhood || "", // 社区
                building: addressComponent.building || "", // 建筑
                adcode: addressComponent.adcode || "", // 行政区划代码
                businessAreas: addressComponent.businessAreas || [], // 商圈信息
                towncode: addressComponent.towncode || "", // 乡镇代码
                originalData: result.regeocode, // 原始数据
              };

              resolve(locationInfo);
            } else {
              console.error(
                `获取位置信息失败: 状态=${status}, 信息=${result?.info || "未知"}`,
              );
              reject(
                new Error(`获取位置信息失败: ${result?.info || "未知错误"}`),
              );
            }
          },
        );
      } catch (apiError: any) {
        console.error("调用 getAddress API 失败:", apiError);
        reject(
          new Error(
            `调用 getAddress API 失败: ${apiError.message || "未知错误"}`,
          ),
        );
      }
    });
  } catch (error) {
    console.error("获取位置信息失败:", error);
    throw error;
  }
};

/**
 * 根据地址获取经纬度（地理编码）
 * @param {string} address - 地址
 * @param {string} city - 城市（可选）
 * @returns {Promise<any>} 返回经纬度信息
 */
export const getCoordsByAddress = async (
  address: string,
  city?: string,
): Promise<any> => {
  console.log(
    `开始根据地址获取经纬度，地址: ${address}${city ? `, 城市: ${city}` : ""}`,
  );

  // 验证地址参数
  if (!address) {
    console.error("地址参数无效");
    throw new Error("地址参数无效");
  }

  try {
    // 获取 Geocoder 实例
    const geocoder = await getGeocoder();
    console.log("成功获取 Geocoder 实例");

    return new Promise((resolve, reject) => {
      // 添加超时处理
      const timeoutId = setTimeout(() => {
        reject(new Error("获取经纬度超时"));
      }, 10000); // 10秒超时

      try {
        console.log(
          `调用 getLocation API，参数: 地址=${address}${city ? `, 城市=${city}` : ""}`,
        );

        geocoder.getLocation(
          address,
          (status: string, result: any) => {
            // 清除超时定时器
            clearTimeout(timeoutId);

            console.log(`地理编码返回状态: ${status}`);
            console.log("地理编码返回结果:", result);

            if (status === "complete" && result.info === "OK") {
              if (result.geocodes && result.geocodes.length > 0) {
                try {
                  const location = result.geocodes[0].location;
                  const formattedAddress =
                    result.geocodes[0].formattedAddress || "";
                  const level = result.geocodes[0].level || "";

                  console.log(
                    `解析到的经纬度: ${location.lng}, ${location.lat}`,
                  );
                  console.log(`解析到的地址: ${formattedAddress}`);

                  const locationInfo = {
                    longitude: location.lng,
                    latitude: location.lat,
                    formattedAddress,
                    level, // 匹配级别
                    originalData: result.geocodes[0], // 原始数据
                  };

                  console.log("经纬度信息处理完成:", locationInfo);
                  resolve(locationInfo);
                } catch (parseError: any) {
                  console.error("解析经纬度信息失败:", parseError);
                  reject(
                    new Error(
                      `解析经纬度信息失败: ${parseError.message || "未知错误"}`,
                    ),
                  );
                }
              } else {
                console.error("未找到匹配的位置信息");
                reject(new Error("未找到匹配的位置信息"));
              }
            } else {
              console.error(
                `获取经纬度失败: 状态=${status}, 信息=${result?.info || "未知"}`,
              );
              reject(
                new Error(`获取经纬度失败: ${result?.info || "未知错误"}`),
              );
            }
          },
          {
            city, // 指定查询的城市
          },
        );
      } catch (apiError: any) {
        // 清除超时定时器
        clearTimeout(timeoutId);
        console.error("调用 getLocation API 失败:", apiError);
        reject(
          new Error(
            `调用 getLocation API 失败: ${apiError.message || "未知错误"}`,
          ),
        );
      }
    });
  } catch (error) {
    console.error("获取经纬度失败:", error);
    throw error;
  }
};

/**
 * 计算两个坐标点之间的距离
 * @param {number} lng1 - 起点经度
 * @param {number} lat1 - 起点纬度
 * @param {number} lng2 - 终点经度
 * @param {number} lat2 - 终点纬度
 * @returns {Promise<number>} 返回距离，单位：米
 */
export const calculateDistance = async (
  lng1: number,
  lat1: number,
  lng2: number,
  lat2: number,
): Promise<number> => {
  try {
    const AMap = await initAMap();
    const start = new AMap.LngLat(lng1, lat1);
    const end = new AMap.LngLat(lng2, lat2);
    const distance = Math.round(start.distance(end));
    return distance;
  } catch (error) {
    console.error("计算距离失败:", error);
    throw error;
  }
};

export default {
  initAMap,
  getGeocoder,
  getLocationByCoords,
  getCoordsByAddress,
  calculateDistance,
};
