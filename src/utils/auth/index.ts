const TokenKey = "student-token";
const CompanyTokenKey = "company-token";
const TokenPrefix = "Bearer ";

function isLogin(key?: string) {
  return !!uni.getStorageSync(key || TokenKey);
}
function getToken(key?: string) {
  return uni.getStorageSync(key || TokenKey);
}
function setToken(token: string, key?: string) {
  uni.setStorageSync(key || TokenKey, token);
}
function clearToken(key?: string) {
  uni.removeStorageSync(key || TokenKey);
}

export {
  clearToken,
  CompanyTokenKey,
  getToken,
  isLogin,
  setToken,
  TokenKey,
  TokenPrefix,
};
