import { getWxJsSdkConfig } from "@/api/common";

interface WxConfig {
  debug: boolean;
  appId: string;
  timestamp: number;
  nonceStr: string;
  signature: string;
  jsApiList: string[];
}

/**
 * 初始化微信 JSSDK
 */
export const initWxJsSdk = async (
  jsApiList: string[] = ["getLocation", "checkJsApi", "saveImageToPhotosAlbum"],
) => {
  try {
    // 获取当前页面 URL
    // const url = "https://career.careermate.com.cn/internship_student/";
    const url = window.location.href.split("#")[0];

    // 获取微信配置
    const config = await getWxJsSdkConfig({ url });

    const wxConfig: WxConfig = {
      debug: false,
      appId: config.appId,
      timestamp: config.timestamp,
      nonceStr: config.nonceStr,
      signature: config.signature,
      jsApiList,
    };
    console.log("微信配置wxConfig:", wxConfig);

    // 配置微信 JSSDK
    await new Promise((resolve, reject) => {
      wx.config(wxConfig);
      wx.ready(() => {
        console.log("微信 JSSDK 初始化成功");
        resolve(true);
      });
      wx.error((err: any) => {
        console.error("微信 JSSDK 初始化失败:", err);
        reject(err);
      });
    });

    return true;
  } catch (error: any) {
    console.error("初始化微信 JSSDK 失败:", error);
    throw error;
  }
};

/**
 * 使用微信 JSSDK 获取位置信息
 */
export const getWxLocation =
  (): Promise<WechatMiniprogram.GetLocationSuccessCallbackResult> => {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: "wgs84",
        success: resolve,
        fail: reject,
      });
    });
  };

/**
 * 保存图片到相册
 * @param localId 图片的本地ID
 */
export const saveImageToPhotosAlbum = (localId: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      localId,
      success: () => {
        uni.showToast({
          title: "保存成功",
          icon: "success",
        });
        resolve();
      },
      fail: (err) => {
        console.error("保存图片失败:", err);
        uni.showToast({
          title: "保存失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};
