import type { AxiosInstance, AxiosResponse } from "axios";
import type { IRequestConfig, IResponse } from "./types";
import { createUniAppAxiosAdapter } from "@uni-helper/axios-adapter";
import axios from "axios";
import { requestInterceptors, responseInterceptors } from "./interceptors";

// 请求实例管理器
class RequestManager {
  private static instance: RequestManager;

  private constructor() {}

  static getInstance(): RequestManager {
    if (!RequestManager.instance) {
      RequestManager.instance = new RequestManager();
    }
    return RequestManager.instance;
  }

  getInstance(baseURL: string, config?: IRequestConfig): AxiosInstance {
    // 每次都创建新的实例，避免配置互相影响
    const instance = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      adapter: createUniAppAxiosAdapter(),
    });

    requestInterceptors(instance);
    responseInterceptors(instance);

    return instance;
  }
}
// 获取默认的baseURL
const getDefaultBaseURL = () => {
  let baseURL = import.meta.env.VITE_API_BASE_URL;
  // #ifdef H5
  if (import.meta.env.VITE_APP_PROXY === "true") {
    baseURL = import.meta.env.VITE_API_PREFIX;
  }
  // #endif
  return baseURL;
};

// 创建请求实例
const createRequest = <T>(
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> => {
  const baseURL = config?.baseURL || getDefaultBaseURL();
  const instance = RequestManager.getInstance().getInstance(baseURL, config);
  console.log("instance===config", config);

  return new Promise<T>((resolve, reject) => {
    instance
      .request(config!)
      .then((res: AxiosResponse<IResponse<T>>) => {
        console.log("[ res ] >", res);
        const { data } = res.data;
        resolve(data);
      })
      .catch((err: any) => {
        console.error("[ err ] >", err);
        reject(err);
      });
  });
};

export function request<T = any>(
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  return createRequest<T>(config);
}

export function get<T = any>(
  url: string,
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  return request<T>({ ...config, url, method: "get" });
}

export function post<T = any>(
  url: string,
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  return request<T>({ ...config, url, method: "post" });
}

export function del<T = any>(
  url: string,
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  return request<T>({ ...config, url, method: "delete" });
}

// 将data转换为FormData
const transformFromData = (data: { [key: string]: string }) => {
  const formData = new FormData();
  for (const key in data) {
    data[key] && formData.append(key, data[key]);
  }
  return formData;
};

export function upload<T = any>(
  url: string,
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  if (config?.data) {
    config.data = transformFromData(config?.data);
  }
  return request<T>({
    headers: {
      "Content-Type": "multipart/form-data;charset=UTF-8",
    },
    ...config,
    url,
    method: "upload",
  });
}

export function download<T = any>(
  url: string,
  config?: IRequestConfig & { baseURL?: string },
): Promise<T> {
  return request<T>({ ...config, url, method: "download" });
}
