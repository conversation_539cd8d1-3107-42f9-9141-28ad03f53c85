import type { IFile } from "./types";

export const mpUploadFile = (
  files: IFile[],
  options: {
    url: string;
    name?: string;
    header?: Record<string, string>;
    formData?: Record<string, any>;
  },
): Promise<{ success: boolean; data?: any; error?: string }> => {
  return new Promise((resolve, reject) => {
    if (!files.length) {
      return reject({ success: false, error: "文件路径不能为空" });
    }
    console.log("mpUploadFile", files, options);
    uni.uploadFile({
      url: options.url, // 默认上传地址
      filePath: files[0].uri, // 默认上传文件路径
      name: options.name || "file",
      header: options.header || {},
      formData: options.formData || {},
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(res.data);
            resolve({ success: true, data: response.data });
          } catch (err) {
            reject({ success: false, error: "服务器返回格式错误" });
          }
        } else {
          reject({ success: false, error: `上传失败: ${res.statusCode}` });
        }
      },
      fail: (err) => {
        console.log("mpUploadFile err", err);
        reject({ success: false, error: err.errMsg || "上传失败" });
      },
    });
  });
};
