// 文件预览工具函数
export const previewFile = (url: string, fileType?: string) => {
  // 提取文件扩展名
  const extension = url.substring(url.lastIndexOf(".") + 1).toLowerCase();

  // 图片预览
  if (
    ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension) ||
    (fileType && fileType.startsWith("image/"))
  ) {
    uni.previewImage({
      urls: [url],
      current: 0,
    });
    return;
  }

  // 视频预览
  if (
    ["mp4", "avi", "mov", "wmv", "flv"].includes(extension) ||
    (fileType && fileType.startsWith("video/"))
  ) {
    uni.navigateTo({
      url: `/pages/common/video-player?url=${encodeURIComponent(url)}`,
    });
    return;
  }

  // 文档预览
  if (
    ["doc", "docx", "pdf", "xls", "xlsx", "ppt", "pptx", "txt"].includes(
      extension,
    )
  ) {
    // 打开文档预览，使用uni-app的文档预览功能
    uni.openDocument({
      filePath: url,
      showMenu: true,
      success: () => {
        console.log("打开文档成功");
      },
      fail: (err) => {
        console.error("打开文档失败", err);
        uni.showToast({
          title: "无法预览此类型文件",
          icon: "none",
        });
      },
    });
    return;
  }

  // 其他类型文件，提示下载
  uni.showModal({
    title: "提示",
    content: "此类型文件不支持预览，是否下载？",
    success: (res) => {
      if (res.confirm) {
        // 下载文件
        uni.downloadFile({
          url,
          success: (res) => {
            if (res.statusCode === 200) {
              // 保存文件
              uni.saveFile({
                tempFilePath: res.tempFilePath,
                success: (res) => {
                  uni.showToast({
                    title: "文件已保存",
                    icon: "success",
                  });
                },
                fail: () => {
                  uni.showToast({
                    title: "保存失败",
                    icon: "none",
                  });
                },
              });
            }
          },
          fail: () => {
            uni.showToast({
              title: "下载失败",
              icon: "none",
            });
          },
        });
      }
    },
  });
};

// 格式化文件大小
export const formatFileSize = (size?: number): string => {
  if (!size) return "未知大小";

  if (size < 1024) {
    return `${size}B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(2)}MB`;
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(2)}GB`;
  }
};

export const base64ToBlob = (base64Data, contentType = "image/png") => {
  // 1. 去除 Base64 前缀（如果存在）
  const base64WithoutPrefix = base64Data.split("base64,")[1] || base64Data;

  // 2. 将 Base64 字符串转为二进制数据
  const byteCharacters = atob(base64WithoutPrefix);
  const byteArrays: any = [];

  // 3. 生成 Uint8Array 数组
  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);
    const byteNumbers: any = Array.from({ length: slice.length });
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }
  const blobUrl = URL.createObjectURL(
    new Blob(byteArrays, { type: contentType }),
  );
  return blobUrl;
};

export const download = (blobUrl) => {
  // 创建下载链接
  const link = document.createElement("a");
  link.href = blobUrl;
  link.download = "image.png"; // 下载文件名
  link.textContent = "点击下载图片";
  document.body.appendChild(link);

  // 使用后释放内存（重要！）
  link.addEventListener("click", () => {
    setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
  });
};
