import type { UploadFile } from "@/components/common/type";

// 小程序更新检测
export function mpUpdate() {
  const updateManager = uni.getUpdateManager();
  updateManager.onCheckForUpdate((res) => {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate);
  });
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: "更新提示",
      content: "检测到新版本，是否下载新版本并重启小程序？",
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        }
      },
    });
  });
  updateManager.onUpdateFailed(() => {
    // 新的版本下载失败
    uni.showModal({
      title: "已经有新版本了哟~",
      content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~",
      showCancel: false,
    });
  });
}

// 解决小程序端真机图片不展示
/**
 * 获取本地图
 * @param folder // 文件夹名字 如 /static/images/home
 * @param fileName // 文件名 如 home-bg
 * @param format // 文件类型 如 png jpg
 * @returns {*|string}
 */

/**
 * 将图片URL转换为base64格式
 * @param {string} imgUrl - 图片路径
 * @returns {string} - base64格式的图片URL或原始URL
 */
export const urlTobase64 = (imgUrl: string): string => {
  // #ifdef MP-WEIXIN
  try {
    // 微信小程序环境下，使用文件系统API读取图片并转换为base64
    const imgBase64 = wx.getFileSystemManager().readFileSync(imgUrl, "base64");
    return `data:image/png;base64,${imgBase64}`;
  } catch (error) {
    console.error("转换图片到base64失败:", error);
    return imgUrl; // 转换失败时返回原始URL
  }
  // #endif

  // 其他环境下，直接返回图片URL
  return imgUrl;
};

const tabBarPages: string[] = [
  "pages/tab/home/<USER>",
  "pages/tab/list/index",
  "pages/tab/user/index",
  "pages/active/signIn/index",
  "pages/active/askLeave/index",
  "pages/active/dailyReport/index",
  "pages/active/weeklyReport/index",
  "pages/active/monthlyReport/index",
  "pages/active/internshipSummary/index",
  "pages/active/visaFree/index",
];

export function isTabBarPage(): boolean {
  const pages = getCurrentPages();
  console.log("isTabBarPage", pages);

  if (!pages.length) return false;

  // 获取当前页面路径（兼容多平台格式）
  const currentPage = pages[pages.length - 1];
  const currentPath = `/${currentPage.route}`.replace(/\/+/g, "/"); // 标准化路径

  return tabBarPages.some((item) => {
    const tabPath = `/${item}`.replace(/\/+/g, "/");
    return currentPath === tabPath;
  });
}

export const dateFormat = (time, mode) => {
  const timeFormat = uni.$u.timeFormat;
  switch (mode) {
    case "datetime":
      return timeFormat(time, "yyyy-mm-dd hh:MM");
    case "date":
      return timeFormat(time, "yyyy-mm-dd");
    case "year-month":
      return timeFormat(time, "yyyy-mm");
    case "time":
      return time;
    default:
      return "";
  }
};

// 计算距离函数
export const Rad = (d) => {
  // 根据经纬度判断距离
  return (d * Math.PI) / 180.0;
};

// 获取坐标距离
export const getDistanceByCoordinate = (location1, location2) => {
  // longitude(经度) and latitude(纬度)
  const lon1 = location1.longitude;
  const lat1 = location1.latitude;
  const lat2 = location2.latitude;
  const lon2 = location2.longitude;
  const radEarth = 6378137;

  const radLat1 = Rad(lat1);
  const radLat2 = Rad(lat2);
  const radLon1 = Rad(lon1);
  const radLon2 = Rad(lon2);

  const difRadLat = radLat1 - radLat2;
  const difRedLon = radLon1 - radLon2;

  let distance =
    2 *
    Math.asin(
      Math.sqrt(
        Math.sin(difRadLat / 2) ** 2 +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(difRedLon / 2) ** 2,
      ),
    );
  distance = distance * radEarth;
  distance = Math.round(distance * 10000) / 10000;
  return distance; // 返回（m)
};

// 获取坐标距离
export const getCoordinateScopeStatusData = (
  referenceCoordinate,
  currentCoordinate,
  scopeNum,
  callback,
) => {
  const scopeNumber = scopeNum || 500;

  const checkInfo = {
    status: false,
    scopeStatus: false,
    curCoordinte: undefined,
  };

  const distanceValue = getDistanceByCoordinate(
    currentCoordinate,
    referenceCoordinate,
  );

  checkInfo.status = true;
  checkInfo.scopeStatus = distanceValue < scopeNumber;
  checkInfo.curCoordinte = currentCoordinate;

  callback && callback(checkInfo);

  return checkInfo.scopeStatus;
};

/**
 * 将附件字符串转换为包含 URL 对象的数组
 * @param str - 附件字符串，多个 URL 用逗号分隔
 * @returns 包含 URL 对象的数组
 */
export function parseStr(str: string | undefined): Partial<UploadFile>[] {
  if (!str) {
    return [];
  }
  return str.split(",").map((item) => ({
    url: item.trim(),
  }));
}

/**
 * 计算两个日期之间的工作日天数（排除周六周日）
 * @param startStr 开始日期字符串，格式 "YYYY/MM/DD 上午"
 * @param endStr 结束日期字符串，格式 "YYYY/MM/DD 上午"
 * @returns 工作日天数
 */
export const calculateWorkingDays = (
  startStr: string,
  endStr: string,
): number => {
  // 增强的日期解析函数
  const parseDate = (str: string) => {
    const [datePart, period] = str.split(" ");
    const [year, month, day] = datePart.split("-").map(Number);
    return {
      date: new Date(year, month - 1, day),
      period: period as "上午" | "下午", // 明确时间段类型
    };
  };

  const startInfo = parseDate(startStr);
  const endInfo = parseDate(endStr);
  const start = startInfo.date;
  const end = endInfo.date;

  let count = 0;

  // 处理逆向日期
  if (start > end) return 0;

  // 处理开始日期（下午时段）
  if (startInfo.period === "下午" && start.getDay() % 6 !== 0) {
    count += 0.5;
  }

  // 处理结束日期（上午时段）
  if (endInfo.period === "上午" && end.getDay() % 6 !== 0) {
    count += 0.5;
  }

  // 处理中间完整天数
  let current = new Date(start);
  current.setDate(current.getDate()); // 从第二天开始

  while (current < end) {
    const day = current.getDay();
    if (day !== 0 && day !== 6) {
      count += 1;
    }
    const nextDate = new Date(current);
    nextDate.setDate(current.getDate() + 1);
    current = nextDate;
  }

  return count;
};

/**
 * 获取当前年份中的周数，并返回周的日期范围
 * @returns {object} 包含 year（年份）、weekNumber（周次）、dateRange（日期范围）的对象
 */
export const getCurrentWeekInfo = () => {
  const today = new Date();
  // 克隆一个日期以便不影响原始值
  const date = new Date(today.getTime());

  // 设置当天为本周的星期一（ISO 周：周一作为一周的第一天）
  const day = date.getDay(); // 当前是星期几（0 是周日，1 是周一）
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 调整到周一
  date.setDate(diff);

  const weekStartDate = new Date(date); // 本周开始日期

  // 计算本周结束日期（+6天）
  const weekEndDate = new Date(date);
  weekEndDate.setDate(weekEndDate.getDate() + 6);

  // 获取年份和月份，确保补零
  const year = weekStartDate.getFullYear();

  // 计算今天是一年中的第几天
  const firstDayOfYear = new Date(year, 0, 1);
  const daysBetweenFirstDayAndStartOfWeek = Math.floor(
    (weekStartDate.getTime() - firstDayOfYear.getTime()) /
      (24 * 60 * 60 * 1000),
  );

  // 第一天是否是周一决定了第一周是否计为第1周
  const firstDayOfTheYearDay = firstDayOfYear.getDay();
  const adjustedFirstDay =
    firstDayOfTheYearDay === 0 ? 7 : firstDayOfTheYearDay; // 周日用7表示
  const weekNumber =
    adjustedFirstDay < 5
      ? Math.ceil((daysBetweenFirstDayAndStartOfWeek + adjustedFirstDay) / 7)
      : Math.ceil(
          (daysBetweenFirstDayAndStartOfWeek + adjustedFirstDay - 7) / 7,
        );

  // 格式化日期显示
  const formatDate = (d: Date): string =>
    `${year}-${(d.getMonth() + 1).toString().padStart(2, "0")}-${d.getDate().toString().padStart(2, "0")}`;
  return {
    year,
    startDate: formatDate(weekStartDate),
    endDate: formatDate(weekEndDate),
    weekNumber,
  };
};

/**
 * 获取当前输入月份的开始日期和结束日期
 * @returns {object} 包含 startDate（月首）和 endDate（月末）的对象
 */
export const getMonthRange = (year, month) => {
  // 创建日期对象
  const startDate = new Date(year, month - 1, 1); // 月份要减1
  const endDate = new Date(year, month, 0); // 下个月的第0天

  // 日期格式化函数
  const format = (date) =>
    [
      date.getFullYear(),
      String(date.getMonth() + 1).padStart(2, "0"),
      String(date.getDate()).padStart(2, "0"),
    ].join("-");

  return {
    start: format(startDate),
    end: format(endDate),
  };
};

/**
 * 根据年份和周次计算出该周的起始和结束日期（周一至周日）
 * @param year 年份
 * @param weekNumber 周次（从1开始计数）
 * @returns {object} 包含 startDate 和 endDate 的对象
 */
export function getWeekDateRange(
  year: number,
  weekNumber: number,
): {
  startDate: string;
  endDate: string;
} {
  // 创建一个日期指向该年的1月4日（ISO 周规则中一定属于第1周）
  const date = new Date(year, 0, 4);

  // 计算当年第一个周一
  const dayOfWeek = date.getDay() || 7; // 周日为0，转换成7
  date.setDate(date.getDate() - (dayOfWeek - 1) + (weekNumber - 1) * 7); // 调整到对应周的周一

  const startDate = new Date(date); // 周一
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 6); // 周日

  // 格式化为 "YYYY-MM-DD"
  const formatDate = (d: Date): string => {
    const month = (d.getMonth() + 1).toString().padStart(2, "0");
    const day = d.getDate().toString().padStart(2, "0");
    return `${d.getFullYear()}-${month}-${day}`;
  };

  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
  };
}

/**
 * 获取当前月份的开始日期和结束日期
 * @returns {object} 包含 startDate（月首）和 endDate（月末）的对象
 */
export const getCurrentMonthDateRange = (): {
  year: number;
  month: number;
  startDate: string;
  endDate: string;
} => {
  const today = new Date();

  // 设置为当前月份的第一天
  const startDate = new Date(today.getFullYear(), today.getMonth(), 1);

  // 设置为当前月份的最后一天
  const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  // 格式化为 "YYYY-MM-DD"
  const formatDate = (d: Date): string => {
    const month = (d.getMonth() + 1).toString().padStart(2, "0");
    const day = d.getDate().toString().padStart(2, "0");
    return `${d.getFullYear()}-${month}-${day}`;
  };

  return {
    year: startDate.getFullYear(),
    month: startDate.getMonth() + 1,
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
  };
};

/**
 * 获取浏览器本地定位
 * 优先使用uni-app的定位API，如果失败则尝试使用浏览器的Geolocation API
 * @param isShowMsg - 是否显示错误提示，默认为false
 * @param useHighAccuracy - 是否使用高精度定位，默认为true
 * @returns 返回包含位置信息的Promise
 */
export const getBrowserLocation = (
  isShowMsg = false,
  useHighAccuracy = true,
): Promise<any> => {
  return new Promise((resolve, reject) => {
    console.log("getBrowserLocation: 开始获取位置");

    // 定义一个通用的错误处理函数
    const handleError = (err: any, source: string) => {
      console.error(`getBrowserLocation(${source}) 错误:`, err);
      let errText = err.message || "获取位置失败";
      const errCode = err.code || -1;
      console.log("errCode", errCode);

      // 处理标准的geolocation错误码
      if (source === "browser") {
        switch (errCode) {
          case 1: // PERMISSION_DENIED
            errText = "用户拒绝地理位置定位请求";
            break;
          case 2: // POSITION_UNAVAILABLE
            errText = "位置信息不可用";
            break;
          case 3: // TIMEOUT
            errText = "获取用户位置的请求超时";
            break;
          default:
            errText = "发生了一个不明错误";
            break;
        }
      }

      if (isShowMsg) {
        uni.showToast({
          title: errText,
          icon: "none",
          duration: 3000,
        });
      }

      return { error: errText, code: errCode, source };
    };

    // 首先尝试使用uni-app的getLocation API
    uni.getLocation({
      type: useHighAccuracy ? "gcj02" : "wgs84",
      isHighAccuracy: useHighAccuracy,
      highAccuracyExpireTime: 5000, // 高精度定位超时时间，单位ms
      success: (res) => {
        console.log("uni.getLocation 成功:", res);
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          altitude: res.altitude,
          verticalAccuracy: res.verticalAccuracy,
          horizontalAccuracy: res.horizontalAccuracy,
          speed: res.speed,
          source: "uni-app",
        });
      },
      fail: (err) => {
        console.warn("uni.getLocation 失败，尝试使用浏览器API:", err);

        // 如果uni-app的API失败，尝试使用浏览器的Geolocation API
        // #ifdef H5
        if (navigator.geolocation) {
          const options = {
            enableHighAccuracy: useHighAccuracy, // 是否使用高精度定位
            timeout: 10000, // 超时时间，单位毫秒
            maximumAge: 0, // 缓存时间，0表示不使用缓存
          };

          navigator.geolocation.getCurrentPosition(
            (res) => {
              console.log("浏览器Geolocation API成功:", res);
              const {
                latitude,
                longitude,
                accuracy,
                altitude,
                altitudeAccuracy,
                speed,
              } = res.coords;
              resolve({
                latitude,
                longitude,
                accuracy,
                altitude,
                verticalAccuracy: altitudeAccuracy,
                horizontalAccuracy: accuracy,
                speed,
                source: "browser",
              });
            },
            (err) => {
              const errorInfo = handleError(err, "browser");
              reject(errorInfo);
            },
            options,
          );
        } else {
          const errorInfo = {
            error: "该浏览器不支持定位",
            code: -2,
            source: "browser",
          };
          if (isShowMsg) {
            uni.showToast({
              title: errorInfo.error,
              icon: "none",
              duration: 3000,
            });
          }
          reject(errorInfo);
        }
        // #endif

        // #ifndef H5
        // 非H5环境，uni.getLocation失败就直接返回错误
        const errorInfo = handleError(err, "uni-app");
        reject(errorInfo);
        // #endif
      },
    });
  });
};

// 判断当前运行环境的工具函数
export const checkRunEnvironment = () => {
  // 判断是否在微信小程序环境
  const isMiniProgram = () => {
    return (
      navigator.userAgent.toLowerCase().includes("miniprogram") ||
      window.__wxjs_environment === "miniprogram"
    );
  };

  // 判断是否在微信浏览器中
  const isWeixinBrowser = () => {
    const ua = navigator.userAgent.toLowerCase();
    return /micromessenger/.test(ua) && !isMiniProgram();
  };

  // 判断是否在H5环境
  const isH5 = () => {
    return !isMiniProgram() && !isWeixinBrowser();
  };

  return {
    isMiniProgram: isMiniProgram(),
    isWeixinBrowser: isWeixinBrowser(),
    isH5: isH5(),
  };
};
