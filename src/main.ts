import App from "@/App.vue";
import setupPlugins from "@/plugins";
import { initVConsole } from "@/utils/vconsole";
import { createSSRApp } from "vue";

import wx from "weixin-js-sdk";

// 引入UnoCSS
import "virtual:uno.css";

window.wx = wx;

// 初始化 vConsole（仅在开发环境和测试环境中启用）
initVConsole();

export function createApp() {
  // 初始化路由拦截
  // routerInterceptor();
  const app = createSSRApp(App);
  app.use(setupPlugins);

  return {
    app,
  };
}
