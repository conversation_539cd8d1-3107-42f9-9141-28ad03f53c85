{
  "compilerOptions": {
    "target": "es2016",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "rootDir": "src", // 指定源代码的根目录
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["src/*"],
      "front-api/*": ["packages/ai-app-platform-api-render/src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "@dcloudio/types",
      "@uni-helper/uni-app-types",
      "miniprogram-api-typings",
      "uview-plus/types"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "outDir": "dist", // 指定编译输出目录
    "sourceMap": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true
  },
  "vueCompilerOptions": {
    "plugins": ["@uni-helper/uni-app-types/volar-plugin"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts"
  ],
  "exclude": ["dist", "node_modules", "uni_modules"]
}
