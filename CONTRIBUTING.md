# 贡献指南

感谢你对本项目的兴趣！我们欢迎任何形式的贡献，无论是报告 bug、提交功能请求、改进文档，还是直接提交代码。为了使贡献过程更加顺利，请按照以下指南进行操作。

## 如何开始

1. **Fork 项目**：首先将项目 `Fork` 到你的 `GitHub` 账户。
2. **克隆到本地**：将你 `Fork` 后的仓库克隆到本地。
   ```bash
   git clone https://github.com/你的用户名/uniapp-vue3-template.git
   ```
3. **创建分支**：在进行任何修改之前，先创建一个新的分支。
   ```bash
   git checkout -b feature/your-feature-name
   ```
4. **进行修改**：根据你的需求进行代码修改或文档编辑。
5. **提交更改**：完成修改后，使用以下命令提交更改：
   ```bash
   pnpm cz
   ```
6. **推送分支**：将你的分支推送到 GitHub。
   ```bash
   git push origin feature/your-feature-name
   ```
7. **创建 Pull Request**：在 `GitHub` 上，提交你的 `Pull Request`，描述清楚你所做的更改。

## 提交代码规范

- **代码格式**：只要你安装了依赖项，就不用担心代码风格。`Git` 钩子会在提交时为你格式化和修复它们。
- **简洁明了的提交信息**：提交信息应简明扼要，说明改动的目的和内容。建议使用以下格式：
  ```
  [类型]：简要说明
  ```
  例如：
  ```
  feat: 添加新功能
  fix: 修复 bug
  docs: 更新文档
  style: 格式调整
  refactor: 代码重构
  test: 添加/修改测试
  ```

## 贡献前请注意

- **讨论功能请求**：在提交功能请求之前，确保这个功能是项目当前的方向。
- **修复 bug**：在修复 `bug` 之前，先检查现有的 `issue` 列表，看看是否有人已经报告了相同的问题。
- **文档改进**：欢迎任何形式的文档改进。文档有时会被遗漏，但它对于项目的可用性至关重要。

## 测试

在提交 `Pull Request` 之前，请确保测试成功通过。如果有修改涉及到核心功能，请确保你的改动不会破坏现有的功能。

## 行为规范

希望在这个项目中创建一个友好和包容的社区。请遵循以下行为准则：

- 尊重他人，避免人身攻击、歧视或骚扰。
- 提供有建设性的反馈，不论是代码还是讨论。
- 对不同的观点保持开放的态度，愿意倾听他人的建议。

## 相关文档

- [项目主页](https://github.com/oyjt/uniapp-vue3-template)
- [问题追踪](https://github.com/oyjt/uniapp-vue3-template/issues)

感谢你的贡献！期待与你的合作！
