import antfu from "@antfu/eslint-config";

export default antfu(
  {
    unocss: true,
    prettier: true,
    ignores: [
      "./dist/*",
      "./.vscode/*",
      "./.idea/*",
      "**/node_modules/*",
      "packages/*",
      "README.md",
      "src/api1/oig-swagger-tool",
      "src/api1/oig-demo-api-render",
    ],
  },
  {
    rules: {
      "style/indent": "off", // 禁用缩进规则，允许混合使用不同的缩进风格
      "style/quotes": ["error", "double"],
      "style/semi": ["error", "always"],
      "style/comma-dangle": ["error", "always-multiline"],
      "style/brace-style": ["error", "1tbs", { allowSingleLine: true }],
      "style/quote-props": ["error", "as-needed"],
      "style/operator-linebreak": "off",
      "style/max-len": "off", // 关闭最大长度限制，交给 Prettier 处理
      "vue/html-self-closing": "off", // 关闭自闭合标签规则，交给 Prettier 处理

      // 其他规则保持不变
      "vue/block-order": [
        "error",
        {
          order: ["template", "script", "style"],
        },
      ],
      // 允许console
      "no-console": "off",
      // 块内的空行
      "padded-blocks": ["error", "never"],
      // 顶级函数应使用 function 关键字声明
      "antfu/top-level-function": "off",
      // 全局的 process 不能用
      "node/prefer-global/process": "off",
      // 禁止未使用的捕获组
      "regexp/no-unused-capturing-group": "off",
      // if 语句后需要换行
      "antfu/if-newline": "off",
      "unused-imports/no-unused-vars": "off",
      "style/arrow-parens": "off",
      "vue/html-closing-bracket-newline": "off", // 禁用闭合标签换行规则
      "vue/singleline-html-element-content-newline": "off", // 禁用单行 HTML 元素内容换行规则
      "vue/multiline-html-element-content-newline": "off", // 禁用多行 HTML 元素内容换行规则
      "vue/no-unused-refs": "off",
      "vue/require-valid-default-prop": "off",
      "style/member-delimiter-style": [
        "error",
        {
          multiline: {
            delimiter: "semi",
            requireLast: true,
          },
          singleline: {
            delimiter: "semi",
            requireLast: false,
          },
        },
      ],
      "new-cap": "off",
      "ts/no-use-before-define": "off",
      "prefer-promise-reject-errors": "off",
      "array-callback-return": "off",
      "no-throw-literal": "off",
      "style/indent-binary-ops": "off",
      "vue/operator-linebreak": "off",
      "vue/html-indent": "off",
    },
  },
);
