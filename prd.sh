
#!/bin/bash
status=0

# 获取远程分支信息
git fetch --quiet

# 检查是否有本地分支与远程分支的差异


# if git diff --quiet origin/$(git branch --show-current) ; then
#     echo "分支没有更新"
# else
#     echo "分支有更新"
#     status=1
# fi


# if [[ $status -eq  0 ]]; then
#     echo "代码没有更新，不执行部署,goodbye!"
#     exit 0
# fi
git pull origin $(git branch --show-current)
sudo docker build -f Dockerfile -t "student-side" .
sudo docker rm -f student-side
sudo docker run --name student-side -d -p 9527:80  student-side
