{"name": "uniapp-vue3-project", "type": "module", "version": "1.3.0", "description": "uniapp 团队协作开发实践模板(Vue3)", "author": {"name": "江阳小道", "email": "<EMAIL>", "github": "https://github.com/oyjt"}, "license": "MIT", "homepage": "https://github.com/oyjt/uniapp-vue3-template", "repository": {"type": "git", "url": "https://github.com/oyjt/uniapp-vue3-template.git"}, "keywords": ["Vue3", "uniapp", "uniapp-vue3-template", "Vite5", "TypeScript", "uview-plus", "uniapp template", "UnoCSS"], "engines": {"node": ">=18", "pnpm": ">=8"}, "scripts": {"preinstall": "npx only-allow pnpm", "uvm": "npx @dcloudio/uvm@latest", "uvm-rm": "node ./scripts/post-upgrade.js", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:h5-test": "uni --mode test", "dev:h5-pro": "uni --mode production", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-weixin-test": "uni -p mp-weixin --mode test", "dev:mp-weixin-prod": "uni -p mp-weixin --mode production", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:h5-test": "uni build  --mode test", "build:h5-prod": "uni build  --mode production", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-weixin-test": "uni build -p mp-weixin --mode test", "build:mp-weixin-prod": "uni build -p mp-weixin --mode production", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "type-check": "vue-tsc --noEmit", "eslint": "eslint \"src/**/*.{js,jsx,ts,tsx,vue}\"", "eslint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx,vue}\" --fix", "stylelint": "stylelint \"src/**/*.{vue,scss,css,sass,less}\"", "stylelint:fix": "stylelint \"src/**/*.{vue,scss,css,sass,less}\" --fix", "cz": "git add . && npx czg", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,less}\"", "prettier:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,less}\"", "postinstall": "simple-git-hooks", "install:all": "sh ./install-all.sh", "install:all:windows": "powershell -ExecutionPolicy Bypass -File .\\install-all.ps1", "update:submodules": "git submodule update --remote --merge", "api": "node src/api1/oig-demo-api-render/index.js", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,less}\" && eslint \"src/**/*.{js,jsx,ts,tsx,vue}\" --fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@dcloudio/uni-app": "3.0.0-4060420250429001", "@dcloudio/uni-app-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-app-plus": "3.0.0-4060420250429001", "@dcloudio/uni-components": "3.0.0-4060420250429001", "@dcloudio/uni-h5": "3.0.0-4060420250429001", "@dcloudio/uni-mp-alipay": "3.0.0-4060420250429001", "@dcloudio/uni-mp-baidu": "3.0.0-4060420250429001", "@dcloudio/uni-mp-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-mp-jd": "3.0.0-4060420250429001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060420250429001", "@dcloudio/uni-mp-lark": "3.0.0-4060420250429001", "@dcloudio/uni-mp-qq": "3.0.0-4060420250429001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060420250429001", "@dcloudio/uni-mp-weixin": "3.0.0-4060420250429001", "@dcloudio/uni-mp-xhs": "3.0.0-4060420250429001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060420250429001", "@qiun/ucharts": "2.5.0-20230101", "@uni-helper/axios-adapter": "^1.5.2", "axios": "^1.7.9", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "lunar-calendar": "^0.1.4", "pinia": "2.2.4", "pinia-plugin-persistedstate": "^3.2.3", "uview-plus": "^3.4.5", "vue": "^3.4.21", "vue-i18n": "^9.14.4", "weixin-js-sdk": "^1.6.5", "z-paging": "^2.8.4"}, "devDependencies": {"@antfu/eslint-config": "3.14.0", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060420250429001", "@dcloudio/uni-cli-shared": "3.0.0-4060420250429001", "@dcloudio/uni-stacktracey": "3.0.0-4060420250429001", "@dcloudio/vite-plugin-uni": "3.0.0-4060420250429001", "@esbuild/darwin-arm64": "0.24.0", "@esbuild/darwin-x64": "0.24.0", "@iconify-json/ep": "^1.2.2", "@iconify-json/mdi": "^1.2.3", "@rollup/rollup-darwin-arm64": "4.31.0", "@rollup/rollup-darwin-x64": "4.31.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.7", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@unocss/eslint-plugin": "^0.63.4", "@unocss/preset-icons": "^0.63.4", "@vue/runtime-core": "^3.4.21", "czg": "^1.11.0", "eslint": "^9.18.0", "eslint-plugin-prettier": "^5.2.3", "lint-staged": "^15.4.1", "miniprogram-api-typings": "^4.0.4", "picocolors": "^1.1.1", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.79.6", "sass-loader": "^16.0.4", "simple-git-hooks": "^2.11.1", "stylelint": "^16.13.2", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "typescript": "^5.7.3", "unocss": "^0.63.6", "unocss-preset-weapp": "^65.4.1", "unplugin-auto-import": "^0.18.5", "unplugin-vue-components": "^0.27.5", "vconsole": "^3.15.1", "vite": "^5.2.8", "vite-plugin-clean-build": "^1.3.0", "vite-plugin-replace-image-url": "^1.3.0", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^2.2.0"}, "pnpm": {"neverBuiltDependencies": ["canvas", "node-gyp"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "node ./scripts/verify-commit.js"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{scss,css,style,html}": ["prettier --write", "stylelint --fix"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"]}}