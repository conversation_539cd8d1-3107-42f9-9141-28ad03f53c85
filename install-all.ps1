Write-Host "===== Starting installation process =====" -ForegroundColor Green

# 1. 初始化子模块
Write-Host "Initializing submodules..." -ForegroundColor Cyan
git submodule init
git submodule update --recursive

# 2. 创建必要的目录结构
New-Item -ItemType Directory -Force -Path packages

# 3. 安装主项目依赖
Write-Host "Installing main project dependencies..." -ForegroundColor Cyan
pnpm install

# 4. 安装 oig-swagger-tool 的依赖
Write-Host "Installing oig-swagger-tool dependencies..." -ForegroundColor Cyan
Set-Location packages/oig-swagger-tool
pnpm install
Set-Location ../..

# 5. 安装 ai-app-platform-api-render 的依赖
Write-Host "Installing ai-app-platform-api-render dependencies..." -ForegroundColor Cyan
Set-Location packages/ai-app-platform-api-render
pnpm install
Set-Location ../..

Write-Host "===== Installation completed! =====" -ForegroundColor Green
