/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActiveItems: typeof import('./../src/components/home/<USER>')['default']
    ActivityControlPanel: typeof import('./../src/components/home/<USER>')['default']
    ApprovalProcess: typeof import('./../src/components/signIn/ApprovalProcess.vue')['default']
    Arcbar: typeof import('./../src/components/common/Arcbar.vue')['default']
    AssessmentManage: typeof import('./../src/components/internshipProgram/AssessmentManage.vue')['default']
    AttachmentDetail: typeof import('./../src/components/internshipApplication/AttachmentDetail.vue')['default']
    AttachmentInfo: typeof import('./../src/components/internshipApplication/AttachmentInfo.vue')['default']
    CardItem: typeof import('./../src/components/common/CardItem.vue')['default']
    Check: typeof import('./../src/components/signIn/Check.vue')['default']
    ClockIn: typeof import('./../src/components/signIn/ClockIn.vue')['default']
    ContentBox: typeof import('./../src/components/common/ContentBox.vue')['default']
    DailyReportApply: typeof import('./../src/components/dailyReport/DailyReportApply.vue')['default']
    DailyReportApplyInfo: typeof import('./../src/components/dailyReport/DailyReportApplyInfo.vue')['default']
    DailyReportContent: typeof import('./../src/components/dailyReport/DailyReportContent.vue')['default']
    DailyReportRecord: typeof import('./../src/components/dailyReport/DailyReportRecord.vue')['default']
    DateBox: typeof import('./../src/components/zsy-calendar/dateBox.vue')['default']
    DetailCard: typeof import('./../src/components/common/DetailCard.vue')['default']
    DetailProgram: typeof import('./../src/components/internshipProgram/DetailProgram.vue')['default']
    EmploymentInfo: typeof import('./../src/components/employment/EmploymentInfo.vue')['default']
    EmploymentInformation: typeof import('./../src/components/employment/EmploymentInformation.vue')['default']
    EmploymentJobInfo: typeof import('./../src/components/employment/EmploymentJobInfo.vue')['default']
    EmploymentRecord: typeof import('./../src/components/employment/EmploymentRecord.vue')['default']
    Empty: typeof import('./../src/components/common/Empty.vue')['default']
    ExplanationAttachment: typeof import('./../src/components/signIn/ExplanationAttachment.vue')['default']
    FeedbackRecord: typeof import('./../src/components/realTimeFeedback/FeedbackRecord.vue')['default']
    FinishModal: typeof import('./../src/components/internshipApplication/FinishModal.vue')['default']
    GradeHead: typeof import('./../src/components/myGrades/GradeHead.vue')['default']
    Information: typeof import('./../src/components/internshipApplication/Information.vue')['default']
    Instructor: typeof import('./../src/components/internshipProgram/Instructor.vue')['default']
    InternshipInfo: typeof import('./../src/components/internshipApplication/InternshipInfo.vue')['default']
    InternshipProgress: typeof import('./../src/components/internshipApplication/InternshipProgress.vue')['default']
    InternshipReason: typeof import('./../src/components/avoidInternship/InternshipReason.vue')['default']
    ItemBox: typeof import('./../src/components/common/ItemBox.vue')['default']
    JobInfo: typeof import('./../src/components/internshipApplication/JobInfo.vue')['default']
    LeaveApply: typeof import('./../src/components/askLeave/LeaveApply.vue')['default']
    LeaveApplyInfo: typeof import('./../src/components/askLeave/LeaveApplyInfo.vue')['default']
    LeaveReason: typeof import('./../src/components/askLeave/LeaveReason.vue')['default']
    LeaveRecord: typeof import('./../src/components/askLeave/LeaveRecord.vue')['default']
    LiuCalendar: typeof import('./../src/components/liu-calendar/index.vue')['default']
    Match: typeof import('./../src/components/internshipApplication/Match.vue')['default']
    MessageItem: typeof import('./../src/components/message/MessageItem.vue')['default']
    MonthlyReportApply: typeof import('./../src/components/monthlyReport/MonthlyReportApply.vue')['default']
    MonthlyReportApplyInfo: typeof import('./../src/components/monthlyReport/MonthlyReportApplyInfo.vue')['default']
    MonthlyReportContent: typeof import('./../src/components/monthlyReport/MonthlyReportContent.vue')['default']
    MonthlyReportRecord: typeof import('./../src/components/monthlyReport/MonthlyReportRecord.vue')['default']
    MyGradesInfo: typeof import('./../src/components/myGrades/MyGradesInfo.vue')['default']
    NavBar: typeof import('./../src/components/common/NavBar.vue')['default']
    NewFeedback: typeof import('./../src/components/realTimeFeedback/NewFeedback.vue')['default']
    NewFeedbackContent: typeof import('./../src/components/realTimeFeedback/NewFeedbackContent.vue')['default']
    NewFeedbackInfo: typeof import('./../src/components/realTimeFeedback/NewFeedbackInfo.vue')['default']
    PaySlipDesc: typeof import('./../src/components/paySlip/PaySlipDesc.vue')['default']
    PaySlipInfo: typeof import('./../src/components/paySlip/PaySlipInfo.vue')['default']
    PreInternshipInfo: typeof import('./../src/components/internshipApplication/PreInternshipInfo.vue')['default']
    Process: typeof import('./../src/components/common/Process.vue')['default']
    ProcessManage: typeof import('./../src/components/internshipProgram/ProcessManage.vue')['default']
    ProgramItem: typeof import('./../src/components/internshipProgram/ProgramItem.vue')['default']
    QuestionnaireItem: typeof import('./../src/components/questionnaire/QuestionnaireItem.vue')['default']
    QuestionSureInfo: typeof import('./../src/components/questionnaire/QuestionSureInfo.vue')['default']
    QuestionTip: typeof import('./../src/components/questionnaire/QuestionTip.vue')['default']
    RadioBox: typeof import('./../src/components/common/RadioBox.vue')['default']
    RatingDetails: typeof import('./../src/components/myGrades/RatingDetails.vue')['default']
    Record: typeof import('./../src/components/signIn/Record.vue')['default']
    RecordReason: typeof import('./../src/components/signIn/RecordReason.vue')['default']
    RecordSteps: typeof import('./../src/components/signIn/RecordSteps.vue')['default']
    ReissueInfo: typeof import('./../src/components/signIn/ReissueInfo.vue')['default']
    Replenish: typeof import('./../src/components/signIn/Replenish.vue')['default']
    RingChart: typeof import('./../src/components/common/RingChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelfRatingDetails: typeof import('./../src/components/myGrades/SelfRatingDetails.vue')['default']
    SlipItem: typeof import('./../src/components/common/SlipItem.vue')['default']
    Steps: typeof import('./../src/components/common/Steps.vue')['default']
    SummaryApply: typeof import('./../src/components/internshipSummary/SummaryApply.vue')['default']
    SummaryApplycontent: typeof import('./../src/components/internshipSummary/SummaryApplycontent.vue')['default']
    SummaryApplyInfo: typeof import('./../src/components/internshipSummary/SummaryApplyInfo.vue')['default']
    SummaryRecord: typeof import('./../src/components/internshipSummary/SummaryRecord.vue')['default']
    TabBar: typeof import('./../src/components/common/TabBar.vue')['default']
    Table: typeof import('./../src/components/common/Table.vue')['default']
    Tip: typeof import('./../src/components/internshipApplication/Tip.vue')['default']
    ToDoList: typeof import('./../src/components/home/<USER>')['default']
    TopHeader: typeof import('./../src/components/common/TopHeader.vue')['default']
    TPicker: typeof import('./../src/components/tPicker/tPicker.vue')['default']
    TutorInfo: typeof import('./../src/components/myGrades/TutorInfo.vue')['default']
    Upload: typeof import('./../src/components/common/Upload.vue')['default']
    UploadImg: typeof import('./../src/components/avoidInternship/UploadImg.vue')['default']
    UploadList: typeof import('./../src/components/common/UploadList.vue')['default']
    VisaFreeApply: typeof import('./../src/components/visaFree/VisaFreeApply.vue')['default']
    VisaFreeApplyContent: typeof import('./../src/components/visaFree/VisaFreeApplyContent.vue')['default']
    VisaFreeApplyInfo: typeof import('./../src/components/visaFree/VisaFreeApplyInfo.vue')['default']
    VisaFreeRecord: typeof import('./../src/components/visaFree/VisaFreeRecord.vue')['default']
    WeeklyReportApply: typeof import('./../src/components/weeklyReport/WeeklyReportApply.vue')['default']
    WeeklyReportApplyInfo: typeof import('./../src/components/weeklyReport/WeeklyReportApplyInfo.vue')['default']
    WeeklyReportContent: typeof import('./../src/components/weeklyReport/WeeklyReportContent.vue')['default']
    WeeklyReportRecord: typeof import('./../src/components/weeklyReport/WeeklyReportRecord.vue')['default']
    ZsyCalendar: typeof import('./../src/components/zsy-calendar/zsy-calendar.vue')['default']
  }
}
