#!/bin/bash

echo "===== Starting installation process ====="

# 1. 初始化子模块
echo "Initializing submodules..."
git submodule init
git submodule update --recursive

# 2. 创建必要的目录结构
# mkdir -p packages
# git <NAME_EMAIL>:foreverHe/oig-swagger-tool.git src/api1/oig-swagger-tool
# git <NAME_EMAIL>:foreverHe/oig-demo-api-render.git src/api1/oig-demo-api-render

# 3. 安装主项目依赖
echo "Installing main project dependencies..."
pnpm install

# 4. 安装 oig-swagger-tool 的依赖
echo "Installing oig-swagger-tool dependencies..."
cd src/api1/oig-swagger-tool
pnpm install
cd ../../../

# 5. 安装 oig-demo-api-render 的依赖
echo "Installing oig-demo-api-render dependencies..."
cd src/api1/oig-demo-api-render
pnpm install
cd ../../../

echo "===== Installation completed! ====="
